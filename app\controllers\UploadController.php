<?php
require_once __DIR__ . '/../models/Berkas.php';
require_once __DIR__ . '/../models/Siswa.php';
require_once __DIR__ . '/../helpers/Security.php';
require_once __DIR__ . '/../helpers/SimpleSessionManager.php';

class UploadController {
    private $berkasModel;
    private $siswaModel;
    private $sessionManager;

    public function __construct() {
        $this->berkasModel = new Berkas();
        $this->siswaModel = new Siswa();
        $this->sessionManager = new SimpleSessionManager();
    }

    /**
     * Show upload form for specific student
     */
    public function berkas($siswaId) {
        Security::requireAuth();

        // Get student data
        $siswa = $this->siswaModel->getById($siswaId);
        if (!$siswa) {
            $_SESSION['error'] = 'Data siswa tidak ditemukan';
            header('Location: /siswa-app/public/siswa');
            exit;
        }

        // Get existing files
        $existingFiles = $this->berkasModel->getBySiswaId($siswaId);

        // Handle form submission
        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            $this->handleUpload($siswaId);
            return;
        }

        $data = [
            'title' => 'Upload Berkas - ' . $siswa['nama_lengkap'],
            'siswa' => $siswa,
            'existing_files' => $existingFiles,
            'file_categories' => $this->berkasModel->getFileCategories(),
            'csrf_token' => Security::generateCSRFToken()
        ];

        $this->view('upload/berkas', $data);
    }

    /**
     * Handle file upload
     */
    private function handleUpload($siswaId) {
        try {
            // Validate CSRF token
            if (!Security::validateCSRFToken($_POST['csrf_token'] ?? '')) {
                throw new Exception('Token keamanan tidak valid');
            }

            $jenisberkas = $_POST['jenis_berkas'] ?? '';
            $keterangan = $_POST['keterangan'] ?? '';

            if (empty($jenisberkas)) {
                throw new Exception('Jenis berkas harus dipilih');
            }

            // Validate file
            if (!isset($_FILES['file']) || $_FILES['file']['error'] !== UPLOAD_ERR_OK) {
                throw new Exception('File tidak berhasil diupload');
            }

            $file = $_FILES['file'];
            $errors = $this->berkasModel->validateFile($file, $jenisberkas);

            if (!empty($errors)) {
                throw new Exception(implode(', ', $errors));
            }

            // Generate descriptive filename
            $originalName = $file['name'];
            $systemName = $this->berkasModel->generateFileName($originalName, $siswaId, $jenisberkas);

            // Upload directory with category subfolder
            $uploadDir = $this->berkasModel->getUploadDir($jenisberkas);
            $filePath = $uploadDir . $systemName;

            // Move uploaded file
            if (!move_uploaded_file($file['tmp_name'], $filePath)) {
                throw new Exception('Gagal menyimpan file');
            }

            // Calculate file hash
            $fileHash = hash_file('sha256', $filePath);

            // Get MIME type
            $finfo = finfo_open(FILEINFO_MIME_TYPE);
            $mimeType = finfo_file($finfo, $filePath);
            finfo_close($finfo);

            // Save to database
            $currentUser = $this->sessionManager->getCurrentUser();
            $berkasData = [
                'siswa_id' => $siswaId,
                'jenis_berkas' => $jenisberkas,
                'nama_berkas' => $originalName,
                'nama_file_asli' => $originalName,
                'nama_file_sistem' => $systemName,
                'ukuran_file' => $file['size'],
                'mime_type' => $mimeType,
                'file_path' => $this->berkasModel->getFullFilePath($jenisberkas, $systemName),
                'file_hash' => $fileHash,
                'keterangan' => $keterangan,
                'uploaded_by' => $currentUser['id'] ?? 1
            ];

            $berkasId = $this->berkasModel->create($berkasData);

            $_SESSION['success'] = 'File berhasil diupload';
            header('Location: /siswa-app/public/upload/berkas/' . $siswaId);
            exit;

        } catch (Exception $e) {
            $_SESSION['error'] = $e->getMessage();
            header('Location: /siswa-app/public/upload/berkas/' . $siswaId);
            exit;
        }
    }

    /**
     * Delete uploaded file
     */
    public function delete($berkasId) {
        Security::requireAuth();

        try {
            $berkas = $this->berkasModel->getById($berkasId);
            if (!$berkas) {
                throw new Exception('File tidak ditemukan');
            }

            // Delete physical file
            $filePath = __DIR__ . '/../../' . $berkas['file_path'];
            if (file_exists($filePath)) {
                unlink($filePath);
            }

            // Delete from database
            $this->berkasModel->delete($berkasId);

            $_SESSION['success'] = 'File berhasil dihapus';
            header('Location: /siswa-app/public/upload/berkas/' . $berkas['siswa_id']);
            exit;

        } catch (Exception $e) {
            $_SESSION['error'] = $e->getMessage();
            header('Location: /siswa-app/public/siswa');
            exit;
        }
    }

    /**
     * Download file
     */
    public function download($berkasId) {
        Security::requireAuth();

        try {
            $berkas = $this->berkasModel->getById($berkasId);
            if (!$berkas) {
                throw new Exception('File tidak ditemukan');
            }

            $filePath = __DIR__ . '/../../' . $berkas['file_path'];
            if (!file_exists($filePath)) {
                throw new Exception('File fisik tidak ditemukan');
            }

            // Set headers for download
            header('Content-Type: ' . $berkas['mime_type']);
            header('Content-Disposition: attachment; filename="' . $berkas['nama_file_asli'] . '"');
            header('Content-Length: ' . filesize($filePath));
            header('Cache-Control: no-cache, must-revalidate');
            header('Expires: 0');

            // Output file
            readfile($filePath);
            exit;

        } catch (Exception $e) {
            $_SESSION['error'] = $e->getMessage();
            header('Location: /siswa-app/public/siswa');
            exit;
        }
    }

    /**
     * Render view
     */
    private function view($path, $data = []) {
        extract($data);
        require_once __DIR__ . '/../views/layouts/header.php';
        require_once __DIR__ . '/../views/' . $path . '.php';
        require_once __DIR__ . '/../views/layouts/footer.php';
    }
}
?>