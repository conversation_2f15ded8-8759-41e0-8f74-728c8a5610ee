<?php
require_once __DIR__ . '/../models/Berkas.php';
require_once __DIR__ . '/../models/Siswa.php';

class UploadController {
    private $berkas;
    private $siswa;

    public function __construct() {
        $this->berkas = new Berkas();
        $this->siswa = new Siswa();
    }

    public function berkas($siswa_id) {
        $data['siswa'] = $this->siswa->getById($siswa_id);
        $data['berkas'] = $this->berkas->getBySiswaId($siswa_id);
        if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_FILES['berkas'])) {
            $file = $_FILES['berkas'];
            if ($file['error'] === UPLOAD_ERR_OK) {
                $upload_dir = __DIR__ . '/../../public/uploads/';
                if (!is_dir($upload_dir)) {
                    mkdir($upload_dir, 0777, true);
                }
                $file_path = 'uploads/' . basename($file['name']);
                if (move_uploaded_file($file['tmp_name'], $upload_dir . basename($file['name']))) {
                    $this->berkas->create($siswa_id, $file['name'], $file_path);
                    header('Location: /siswa/detail/' . $siswa_id);
                    exit;
                }
            }
        }
        $this->view('upload/berkas', $data);
    }

    private function view($path, $data = []) {
        extract($data);
        require_once __DIR__ . '/../views/layouts/header.php';
        require_once __DIR__ . '/../views/' . $path . '.php';
        require_once __DIR__ . '/../views/layouts/footer.php';
    }
}
?>