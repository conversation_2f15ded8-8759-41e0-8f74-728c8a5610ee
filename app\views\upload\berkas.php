<?php
// Check if siswa data exists
if (!isset($siswa) || empty($siswa)) {
    echo '<div class="alert alert-danger">Data siswa tidak ditemukan.</div>';
    return;
}
?>

<div class="container-fluid">
    <!-- Page Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="h3 mb-0 text-gray-800">
                        <i class="bi bi-cloud-upload text-primary"></i>
                        Upload Berkas Siswa
                    </h1>
                    <p class="text-muted mb-0">
                        Upload dokumen untuk: <strong><?= htmlspecialchars($siswa['nama_lengkap'] ?? 'N/A') ?></strong>
                        (NIS: <?= htmlspecialchars($siswa['nis'] ?? 'N/A') ?>)
                    </p>
                </div>
                <div>
                    <a href="/siswa-app/public/siswa/detail/<?= $siswa['id_siswa'] ?? $siswa['id'] ?>" class="btn btn-secondary">
                        <i class="bi bi-arrow-left"></i>
                        Kembali ke Detail
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Alert Messages -->
    <?php if (isset($_SESSION['success'])): ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="bi bi-check-circle"></i>
            <?= htmlspecialchars($_SESSION['success']) ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
        <?php unset($_SESSION['success']); ?>
    <?php endif; ?>

    <?php if (isset($_SESSION['error'])): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="bi bi-exclamation-triangle"></i>
            <?= htmlspecialchars($_SESSION['error']) ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
        <?php unset($_SESSION['error']); ?>
    <?php endif; ?>

    <div class="row">
        <!-- Upload Form -->
        <div class="col-lg-8 mb-4">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="bi bi-upload"></i>
                        Upload Dokumen Baru
                    </h6>
                </div>
                <div class="card-body">
                    <form method="POST" enctype="multipart/form-data" id="uploadForm">
                        <input type="hidden" name="csrf_token" value="<?= $csrf_token ?>">

                        <!-- File Type Selection -->
                        <div class="mb-4">
                            <label class="form-label"><strong>Jenis Dokumen</strong></label>
                            <?php foreach ($file_categories as $category => $types): ?>
                                <div class="mb-3">
                                    <h6 class="text-muted"><?= $category ?></h6>
                                    <div class="row">
                                        <?php foreach ($types as $key => $label): ?>
                                            <div class="col-md-6 mb-2">
                                                <div class="form-check">
                                                    <input class="form-check-input" type="radio" name="jenis_berkas"
                                                           id="<?= $key ?>" value="<?= $key ?>" required>
                                                    <label class="form-check-label" for="<?= $key ?>">
                                                        <?= $label ?>
                                                    </label>
                                                </div>
                                            </div>
                                        <?php endforeach; ?>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>

                        <!-- File Upload -->
                        <div class="mb-3">
                            <label for="file" class="form-label"><strong>Pilih File</strong></label>
                            <input type="file" class="form-control" id="file" name="file" required
                                   accept=".pdf,.jpg,.jpeg,.png,.gif,.doc,.docx">
                            <div class="form-text">
                                <i class="bi bi-info-circle"></i>
                                Format yang diizinkan: PDF, JPG, PNG, GIF, DOC, DOCX (Maksimal 5MB)
                            </div>
                        </div>

                        <!-- File Preview -->
                        <div id="filePreview" class="mb-3" style="display: none;">
                            <div class="card bg-light">
                                <div class="card-body">
                                    <h6>Preview File:</h6>
                                    <div id="previewContent"></div>
                                </div>
                            </div>
                        </div>

                        <!-- Description -->
                        <div class="mb-3">
                            <label for="keterangan" class="form-label">Keterangan (Opsional)</label>
                            <textarea class="form-control" id="keterangan" name="keterangan" rows="3"
                                      placeholder="Tambahkan keterangan atau catatan untuk dokumen ini..."></textarea>
                        </div>

                        <!-- Submit Button -->
                        <div class="d-grid">
                            <button type="submit" class="btn btn-primary btn-lg">
                                <i class="bi bi-cloud-upload"></i>
                                Upload Dokumen
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- Existing Files -->
        <div class="col-lg-4">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="bi bi-files"></i>
                        Dokumen yang Sudah Ada
                    </h6>
                </div>
                <div class="card-body">
                    <?php if (!empty($existing_files)): ?>
                        <div class="list-group">
                            <?php
                            $allowedTypes = (new Berkas())->getAllowedTypes();
                            foreach ($existing_files as $file):
                            ?>
                                <div class="list-group-item">
                                    <div class="d-flex justify-content-between align-items-start">
                                        <div class="flex-grow-1">
                                            <h6 class="mb-1">
                                                <?= htmlspecialchars($file['nama_berkas'] ?? 'File') ?>
                                            </h6>
                                            <p class="mb-1 text-muted small">
                                                <?= $allowedTypes[$file['jenis_berkas']] ?? ucfirst(str_replace('_', ' ', $file['jenis_berkas'])) ?>
                                            </p>
                                            <small class="text-muted">
                                                <?= number_format(($file['ukuran_file'] ?? 0) / 1024, 1) ?> KB
                                                • <?= date('d/m/Y H:i', strtotime($file['created_at'] ?? 'now')) ?>
                                            </small>
                                        </div>
                                        <div class="btn-group-vertical btn-group-sm">
                                            <a href="/siswa-app/public/upload/download/<?= $file['id'] ?>"
                                               class="btn btn-outline-primary btn-sm" title="Download">
                                                <i class="bi bi-download"></i>
                                            </a>
                                            <a href="/siswa-app/public/upload/delete/<?= $file['id'] ?>"
                                               class="btn btn-outline-danger btn-sm" title="Hapus"
                                               onclick="return confirm('Yakin ingin menghapus file ini?')">
                                                <i class="bi bi-trash"></i>
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    <?php else: ?>
                        <div class="text-center py-4">
                            <i class="bi bi-file-earmark" style="font-size: 3rem; color: #dee2e6;"></i>
                            <p class="text-muted mt-2 mb-0">Belum ada dokumen</p>
                            <small class="text-muted">Upload dokumen pertama untuk siswa ini</small>
                        </div>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Upload Guidelines -->
            <div class="card shadow mt-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-info">
                        <i class="bi bi-info-circle"></i>
                        Panduan Upload
                    </h6>
                </div>
                <div class="card-body">
                    <ul class="list-unstyled mb-0">
                        <li class="mb-2">
                            <i class="bi bi-check-circle text-success"></i>
                            <small>Format: PDF, JPG, PNG, DOC</small>
                        </li>
                        <li class="mb-2">
                            <i class="bi bi-check-circle text-success"></i>
                            <small>Ukuran maksimal: 5MB</small>
                        </li>
                        <li class="mb-2">
                            <i class="bi bi-check-circle text-success"></i>
                            <small>Scan dengan kualitas baik</small>
                        </li>
                        <li class="mb-2">
                            <i class="bi bi-check-circle text-success"></i>
                            <small>Pastikan dokumen jelas terbaca</small>
                        </li>
                        <li class="mb-0">
                            <i class="bi bi-check-circle text-success"></i>
                            <small>Gunakan nama file yang deskriptif</small>
                        </li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// File preview functionality
document.getElementById('file').addEventListener('change', function(e) {
    const file = e.target.files[0];
    const preview = document.getElementById('filePreview');
    const previewContent = document.getElementById('previewContent');

    if (file) {
        preview.style.display = 'block';

        const fileInfo = `
            <div class="d-flex align-items-center">
                <i class="bi bi-file-earmark-text me-2" style="font-size: 2rem;"></i>
                <div>
                    <strong>${file.name}</strong><br>
                    <small class="text-muted">
                        ${(file.size / 1024).toFixed(1)} KB • ${file.type || 'Unknown type'}
                    </small>
                </div>
            </div>
        `;

        previewContent.innerHTML = fileInfo;

        // Show image preview for image files
        if (file.type.startsWith('image/')) {
            const reader = new FileReader();
            reader.onload = function(e) {
                previewContent.innerHTML += `
                    <div class="mt-2">
                        <img src="${e.target.result}" class="img-thumbnail" style="max-height: 200px;">
                    </div>
                `;
            };
            reader.readAsDataURL(file);
        }
    } else {
        preview.style.display = 'none';
    }
});

// Form validation
document.getElementById('uploadForm').addEventListener('submit', function(e) {
    const fileInput = document.getElementById('file');
    const jenisberkas = document.querySelector('input[name="jenis_berkas"]:checked');

    if (!fileInput.files[0]) {
        e.preventDefault();
        alert('Silakan pilih file yang akan diupload');
        return;
    }

    if (!jenisberkas) {
        e.preventDefault();
        alert('Silakan pilih jenis dokumen');
        return;
    }

    // Check file size
    if (fileInput.files[0].size > 5 * 1024 * 1024) {
        e.preventDefault();
        alert('Ukuran file terlalu besar. Maksimal 5MB');
        return;
    }
});
</script>