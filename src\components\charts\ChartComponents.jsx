import React from 'react';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend,
  ArcElement,
  PointElement,
  LineElement,
} from 'chart.js';
import { Bar, Doughnut, Line } from 'react-chartjs-2';

// Register Chart.js components
ChartJS.register(
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend,
  ArcElement,
  PointElement,
  LineElement
);

// Chart options
const defaultOptions = {
  responsive: true,
  maintainAspectRatio: false,
  plugins: {
    legend: {
      position: 'top',
    },
  },
};

// Siswa Status Chart (Doughnut)
export const SiswaStatusChart = ({ data }) => {
  const chartData = {
    labels: ['Aktif', 'Lulus', 'Mutasi', 'Dropout'],
    datasets: [
      {
        data: [
          data?.aktif || 0,
          data?.lulus || 0,
          data?.mutasi || 0,
          data?.dropout || 0,
        ],
        backgroundColor: [
          '#10B981', // Green for Aktif
          '#3B82F6', // Blue for Lulus
          '#F59E0B', // Yellow for Mutasi
          '#EF4444', // Red for Dropout
        ],
        borderColor: [
          '#059669',
          '#2563EB',
          '#D97706',
          '#DC2626',
        ],
        borderWidth: 2,
      },
    ],
  };

  const options = {
    ...defaultOptions,
    plugins: {
      ...defaultOptions.plugins,
      title: {
        display: true,
        text: 'Status Siswa',
        font: {
          size: 16,
          weight: 'bold',
        },
      },
    },
  };

  return (
    <div className="bg-white p-4 rounded-lg shadow-md">
      <div className="h-64">
        <Doughnut data={chartData} options={options} />
      </div>
    </div>
  );
};

// Siswa per Kelas Chart (Bar)
export const SiswaPerKelasChart = ({ data }) => {
  const chartData = {
    labels: data?.map(item => item.kelas) || [],
    datasets: [
      {
        label: 'Jumlah Siswa',
        data: data?.map(item => item.jumlah) || [],
        backgroundColor: 'rgba(59, 130, 246, 0.8)',
        borderColor: 'rgba(59, 130, 246, 1)',
        borderWidth: 1,
      },
    ],
  };

  const options = {
    ...defaultOptions,
    plugins: {
      ...defaultOptions.plugins,
      title: {
        display: true,
        text: 'Distribusi Siswa per Kelas',
        font: {
          size: 16,
          weight: 'bold',
        },
      },
    },
    scales: {
      y: {
        beginAtZero: true,
        ticks: {
          stepSize: 1,
        },
      },
    },
  };

  return (
    <div className="bg-white p-4 rounded-lg shadow-md">
      <div className="h-64">
        <Bar data={chartData} options={options} />
      </div>
    </div>
  );
};

// Trend Pendaftaran Chart (Line)
export const TrendPendaftaranChart = ({ data }) => {
  const chartData = {
    labels: data?.map(item => item.bulan) || [],
    datasets: [
      {
        label: 'Pendaftaran Siswa',
        data: data?.map(item => item.jumlah) || [],
        borderColor: 'rgb(16, 185, 129)',
        backgroundColor: 'rgba(16, 185, 129, 0.2)',
        tension: 0.4,
        fill: true,
      },
    ],
  };

  const options = {
    ...defaultOptions,
    plugins: {
      ...defaultOptions.plugins,
      title: {
        display: true,
        text: 'Trend Pendaftaran Siswa (2024)',
        font: {
          size: 16,
          weight: 'bold',
        },
      },
    },
    scales: {
      y: {
        beginAtZero: true,
        ticks: {
          stepSize: 1,
        },
      },
    },
  };

  return (
    <div className="bg-white p-4 rounded-lg shadow-md">
      <div className="h-64">
        <Line data={chartData} options={options} />
      </div>
    </div>
  );
};

// Gender Distribution Chart (Doughnut)
export const GenderDistributionChart = ({ data }) => {
  const chartData = {
    labels: ['Laki-laki', 'Perempuan'],
    datasets: [
      {
        data: [
          data?.L || 0,
          data?.P || 0,
        ],
        backgroundColor: [
          '#3B82F6', // Blue for Male
          '#EC4899', // Pink for Female
        ],
        borderColor: [
          '#2563EB',
          '#DB2777',
        ],
        borderWidth: 2,
      },
    ],
  };

  const options = {
    ...defaultOptions,
    plugins: {
      ...defaultOptions.plugins,
      title: {
        display: true,
        text: 'Distribusi Gender',
        font: {
          size: 16,
          weight: 'bold',
        },
      },
    },
  };

  return (
    <div className="bg-white p-4 rounded-lg shadow-md">
      <div className="h-64">
        <Doughnut data={chartData} options={options} />
      </div>
    </div>
  );
};

// Statistics Cards Component
export const StatCard = ({ title, value, icon, color = 'blue', trend, subtitle }) => {
  const colorClasses = {
    blue: 'bg-blue-500 text-blue-600 bg-blue-50',
    green: 'bg-green-500 text-green-600 bg-green-50',
    yellow: 'bg-yellow-500 text-yellow-600 bg-yellow-50',
    red: 'bg-red-500 text-red-600 bg-red-50',
    purple: 'bg-purple-500 text-purple-600 bg-purple-50',
  };

  const [bgColor, textColor, cardBg] = colorClasses[color].split(' ');

  return (
    <div className={`${cardBg} rounded-lg p-6 shadow-md border border-gray-200`}>
      <div className="flex items-center justify-between">
        <div className="flex-1">
          <p className="text-sm font-medium text-gray-600 mb-1">{title}</p>
          <p className="text-2xl font-bold text-gray-900">{value}</p>
          {subtitle && (
            <p className="text-xs text-gray-500 mt-1">{subtitle}</p>
          )}
          {trend && (
            <div className="flex items-center mt-2">
              <span className={`text-xs font-medium ${trend.positive ? 'text-green-600' : 'text-red-600'}`}>
                {trend.positive ? '↗' : '↘'} {trend.value}
              </span>
              <span className="text-xs text-gray-500 ml-1">{trend.period}</span>
            </div>
          )}
        </div>
        <div className={`w-12 h-12 ${bgColor} rounded-lg flex items-center justify-center`}>
          <span className="text-white text-xl">{icon}</span>
        </div>
      </div>
    </div>
  );
};
