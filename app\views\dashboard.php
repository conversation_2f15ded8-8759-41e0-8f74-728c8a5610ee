<?php
require_once __DIR__ . '/../../app/models/Siswa.php';
require_once __DIR__ . '/../../app/models/Kelas.php';
require_once __DIR__ . '/../../app/models/Berkas.php';

$siswaModel = new Siswa();
$kelasModel = new Kelas();
$berkasModel = new Berkas();

$totalSiswa = count($siswaModel->getAll());
$totalKelas = count($kelasModel->getAll());
$totalBerkas = count($berkasModel->getBySiswaId(0)); // 0 untuk mengambil semua berkas
$siswaPerKelas = $siswaModel->getAll(); // Untuk grafik
?>
<h2>Dashboard</h2>
<div class="dashboard-container">
    <div class="card">
        <h3>Total Students</h3>
        <p><?= $totalSiswa ?></p>
        <a href="/siswa">View Students</a>
    </div>
    <div class="card">
        <h3>Total Classes</h3>
        <p><?= $totalKelas ?></p>
        <a href="/kelas">View Classes</a>
    </div>
    <div class="card">
        <h3>Total Files</h3>
        <p><?= $totalBerkas ?></p>
        <a href="/siswa">Upload Files</a>
    </div>
</div>

<h3>Students per Class</h3>
<canvas id="siswaPerKelasChart"></canvas>

<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
    const ctx = document.getElementById('siswaPerKelasChart').getContext('2d');
    const chart = new Chart(ctx, {
        type: 'bar',
        data: {
            labels: [<?php
                $labels = array_unique(array_column($siswaPerKelas, 'nama_kelas'));
                echo "'" . implode("','", array_map('htmlspecialchars', $labels)) . "'";
            ?>],
            datasets: [{
                label: 'Number of Students',
                data: [<?php
                    $counts = array_count_values(array_column($siswaPerKelas, 'nama_kelas'));
                    echo implode(',', array_values($counts));
                ?>],
                backgroundColor: ['#4CAF50', '#2196F3', '#FF9800', '#F44336'],
                borderColor: ['#388E3C', '#1976D2', '#F57C00', '#D32F2F'],
                borderWidth: 1
            }]
        },
        options: {
            scales: {
                y: {
                    beginAtZero: true,
                    title: {
                        display: true,
                        text: 'Number of Students'
                    }
                },
                x: {
                    title: {
                        display: true,
                        text: 'Classes'
                    }
                }
            }
        }
    });
</script>