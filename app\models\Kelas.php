<?php
require_once __DIR__ . '/Database.php';

class Kelas {
    private $db;

    public function __construct() {
        $this->db = new Database();
    }

    public function getAll() {
        return $this->db->fetchAll("SELECT id as id_kelas, nama_kelas, tingkat, jurusan, tahun_pelajaran, wali_kelas FROM kelas WHERE is_active = 1 ORDER BY tingkat, nama_kelas");
    }

    public function getById($id) {
        return $this->db->fetch("SELECT id as id_kelas, nama_kelas, tingkat, jurusan, tahun_pelajaran, wali_kelas FROM kelas WHERE id = ?", [$id]);
    }

    public function create($nama_kelas) {
        $this->db->query("INSERT INTO kelas (nama_kelas) VALUES (?)", [$nama_kelas]);
        return $this->db->lastInsertId();
    }
}
?>