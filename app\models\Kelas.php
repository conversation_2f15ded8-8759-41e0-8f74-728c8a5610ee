<?php
require_once __DIR__ . '/Database.php';

class Kelas {
    private $db;

    public function __construct() {
        $this->db = new Database();
    }

    public function getAll() {
        return $this->db->fetchAll("SELECT * FROM kelas");
    }

    public function create($nama_kelas) {
        $this->db->query("INSERT INTO kelas (nama_kelas) VALUES (?)", [$nama_kelas]);
        return $this->db->lastInsertId();
    }
}
?>