<?php
/**
 * Create Category Folders for Document Organization
 */

echo "<h2>📁 Create Document Category Folders</h2>";

$basePath = __DIR__ . '/uploads/berkas/';

// Document categories
$categories = [
    'identitas' => '<PERSON>kumen Identitas (Kartu Keluarga, Akta Kelahira<PERSON>)',
    'rapor' => 'Rapor (Kelas X, XI, XII)',
    'ijazah' => 'Ijazah (SD, SMP, SMA)',
    'foto' => 'Foto Siswa',
    'surat' => 'Surat-surat (Keterangan Sehat, Kelakuan Baik)',
    'prestasi' => 'Piagam Prestasi',
    'lainnya' => 'Dokumen Lainnya'
];

echo "<p>Base upload path: <code>$basePath</code></p>";

// Create base folder if not exists
if (!is_dir($basePath)) {
    if (mkdir($basePath, 0755, true)) {
        echo "<p style='color: green;'>✅ Created base folder: <strong>uploads/berkas/</strong></p>";
    } else {
        echo "<p style='color: red;'>❌ Failed to create base folder</p>";
        exit;
    }
} else {
    echo "<p style='color: blue;'>ℹ️ Base folder already exists</p>";
}

// Create category folders
foreach ($categories as $folder => $description) {
    $fullPath = $basePath . $folder;
    
    if (!is_dir($fullPath)) {
        if (mkdir($fullPath, 0755, true)) {
            echo "<p style='color: green;'>✅ Created category folder: <strong>$folder/</strong></p>";
            echo "<p style='color: gray; margin-left: 20px; font-size: 12px;'>$description</p>";
        } else {
            echo "<p style='color: red;'>❌ Failed to create folder: <strong>$folder/</strong></p>";
        }
    } else {
        echo "<p style='color: blue;'>ℹ️ Category folder already exists: <strong>$folder/</strong></p>";
    }
}

// Create .htaccess for each category folder
$htaccessContent = "# Security for uploaded files
Options -Indexes
<Files *.php>
    Deny from all
</Files>

# Allow only specific file types
<FilesMatch \"\\.(pdf|jpg|jpeg|png|gif|doc|docx)$\">
    Allow from all
</FilesMatch>";

foreach ($categories as $folder => $description) {
    $htaccessPath = $basePath . $folder . '/.htaccess';
    
    if (!file_exists($htaccessPath)) {
        if (file_put_contents($htaccessPath, $htaccessContent)) {
            echo "<p style='color: green; font-size: 12px;'>✅ Created .htaccess for $folder/</p>";
        } else {
            echo "<p style='color: red; font-size: 12px;'>❌ Failed to create .htaccess for $folder/</p>";
        }
    }
}

echo "<h3>📋 Folder Structure Preview</h3>";
echo "<pre style='background: #f8f9fa; padding: 15px; border-radius: 5px;'>";
echo "uploads/berkas/\n";
foreach ($categories as $folder => $description) {
    echo "├── $folder/\n";
    echo "│   ├── .htaccess\n";
    echo "│   └── (uploaded files will be here)\n";
}
echo "</pre>";

echo "<h3>📝 File Naming Examples</h3>";
echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px;'>";

$examples = [
    'kartu_keluarga' => 'Kartu_Keluarga_Siswa_1_20250602123456_abc123.pdf',
    'rapor_kelas_x' => 'Rapor_Kelas_X_Siswa_1_20250602123500_def456.pdf',
    'ijazah_smp' => 'Ijazah_SMP_Siswa_1_20250602123600_ghi789.pdf',
    'foto_siswa' => 'Foto_Siswa_Siswa_1_20250602123700_jkl012.jpg'
];

foreach ($examples as $type => $filename) {
    require_once 'app/models/Berkas.php';
    $berkasModel = new Berkas();
    $category = $berkasModel->getDocumentCategory($type);
    
    echo "<p><strong>$type:</strong></p>";
    echo "<p style='margin-left: 20px; font-family: monospace; color: #666;'>";
    echo "uploads/berkas/$category/$filename";
    echo "</p>";
}

echo "</div>";

echo "<p style='color: green; font-weight: bold; font-size: 18px;'>🎉 Document category folders created successfully!</p>";

echo "<h3>🧪 Test the System</h3>";
echo "<p><a href='simple_upload.php?siswa_id=1' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>🔗 Test Upload</a></p>";
echo "<p><a href='public/upload/berkas/1' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>📁 Full Upload System</a></p>";
echo "<p><a href='public/siswa/detail/1' style='background: #17a2b8; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>👤 Student Detail</a></p>";
?>
