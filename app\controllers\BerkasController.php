<?php
require_once __DIR__ . '/../models/Berkas.php';
require_once __DIR__ . '/../models/Siswa.php';
require_once __DIR__ . '/../helpers/Security.php';

class BerkasController {
    private $berkasModel;
    private $siswaModel;

    public function __construct() {
        $this->berkasModel = new Berkas();
        $this->siswaModel = new Siswa();
    }

    /**
     * Show all files for a student
     */
    public function index($siswaId) {
        Security::requireAuth();
        
        // Get student data
        $siswa = $this->siswaModel->getById($siswaId);
        if (!$siswa) {
            $_SESSION['error'] = 'Data siswa tidak ditemukan';
            header('Location: /siswa-app/public/siswa');
            exit;
        }

        // Get all files for this student
        $berkas = $this->berkasModel->getBySiswaId($siswaId);
        
        $data = [
            'title' => 'Berkas Siswa - ' . $siswa['nama_lengkap'],
            'siswa' => $siswa,
            'berkas' => $berkas
        ];

        $this->view('berkas/index', $data);
    }

    /**
     * Show files by category
     */
    public function category($siswaId, $category) {
        Security::requireAuth();
        
        // Get student data
        $siswa = $this->siswaModel->getById($siswaId);
        if (!$siswa) {
            $_SESSION['error'] = 'Data siswa tidak ditemukan';
            header('Location: /siswa-app/public/siswa');
            exit;
        }

        // Get files by category
        $berkas = $this->berkasModel->getBySiswaIdAndCategory($siswaId, $category);
        
        $data = [
            'title' => 'Berkas ' . ucfirst($category) . ' - ' . $siswa['nama_lengkap'],
            'siswa' => $siswa,
            'berkas' => $berkas,
            'category' => $category
        ];

        $this->view('berkas/category', $data);
    }

    /**
     * Render view
     */
    private function view($path, $data = []) {
        extract($data);
        require_once __DIR__ . '/../views/layouts/header.php';
        require_once __DIR__ . '/../views/' . $path . '.php';
        require_once __DIR__ . '/../views/layouts/footer.php';
    }
}
?>
