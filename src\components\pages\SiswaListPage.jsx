import React, { useEffect, useState, useCallback } from 'react';
import { Link } from 'react-router-dom';
import api from '../../services/api';
import MainLayout from '../layouts/MainLayout';
import { useDebounce } from '../../hooks/useFetch';
import { validateSearchQuery } from '../../utils/validation';

const SiswaListPage = () => {
  const [siswaList, setSiswaList] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [statusFilter, setStatusFilter] = useState('');
  const [kelasFilter, setKelasFilter] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [totalSiswa, setTotalSiswa] = useState(0);

  // Debounce search query
  const debouncedSearchQuery = useDebounce(searchQuery, 500);

  const fetchSiswa = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);

      // Validate search query if exists
      if (debouncedSearchQuery) {
        const validation = validateSearchQuery(debouncedSearchQuery);
        if (!validation.isValid) {
          setError(validation.error);
          setLoading(false);
          return;
        }
      }

      const params = {
        page: currentPage,
        limit: 10,
        search: debouncedSearchQuery,
        status: statusFilter,
        kelas: kelasFilter
      };

      const res = await api.getSiswaList(params);
      setSiswaList(res.data.data || []);

      // Handle pagination if API returns it
      if (res.data.pagination) {
        setTotalPages(res.data.pagination.totalPages);
        setTotalSiswa(res.data.pagination.total);
      }
    } catch (err) {
      console.error('Error fetching siswa:', err);
      setError('Gagal memuat data siswa. Silakan coba lagi.');
    } finally {
      setLoading(false);
    }
  }, [debouncedSearchQuery, statusFilter, kelasFilter, currentPage]);

  useEffect(() => {
    fetchSiswa();
  }, [fetchSiswa]);

  if (loading) {
    return (
      <MainLayout>
        <div className="flex justify-center items-center py-12">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
          <span className="ml-3 text-gray-600">Memuat data siswa...</span>
        </div>
      </MainLayout>
    );
  }

  if (error) {
    return (
      <MainLayout>
        <div className="bg-red-50 border border-red-200 rounded-lg p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <svg className="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
              </svg>
            </div>
            <div className="ml-3">
              <h3 className="text-sm font-medium text-red-800">Error</h3>
              <p className="text-sm text-red-700 mt-1">{error}</p>
            </div>
          </div>
        </div>
      </MainLayout>
    );
  }

  return (
    <MainLayout>
      <div className="max-w-6xl mx-auto">
        <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center mb-6 gap-4">
          <h2 className="text-2xl sm:text-3xl font-bold text-gray-800">Daftar Siswa</h2>
          <button className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors text-sm sm:text-base">
            + Tambah Siswa
          </button>
        </div>

        {/* Search and Filter Section */}
        <div className="bg-white rounded-lg shadow-md p-4 sm:p-6 mb-6">
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
            {/* Search Input */}
            <div className="sm:col-span-2">
              <label htmlFor="search" className="block text-sm font-medium text-gray-700 mb-2">
                Cari Siswa
              </label>
              <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <svg className="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                  </svg>
                </div>
                <input
                  type="text"
                  id="search"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                  placeholder="Cari berdasarkan nama atau NIS..."
                />
              </div>
            </div>

            {/* Status Filter */}
            <div>
              <label htmlFor="status" className="block text-sm font-medium text-gray-700 mb-2">
                Status
              </label>
              <select
                id="status"
                value={statusFilter}
                onChange={(e) => setStatusFilter(e.target.value)}
                className="block w-full px-3 py-2 border border-gray-300 rounded-md leading-5 bg-white focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
              >
                <option value="">Semua Status</option>
                <option value="aktif">Aktif</option>
                <option value="lulus">Lulus</option>
                <option value="mutasi">Mutasi</option>
                <option value="dropout">Dropout</option>
              </select>
            </div>

            {/* Kelas Filter */}
            <div>
              <label htmlFor="kelas" className="block text-sm font-medium text-gray-700 mb-2">
                Kelas
              </label>
              <select
                id="kelas"
                value={kelasFilter}
                onChange={(e) => setKelasFilter(e.target.value)}
                className="block w-full px-3 py-2 border border-gray-300 rounded-md leading-5 bg-white focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
              >
                <option value="">Semua Kelas</option>
                <option value="10 IPA 1">10 IPA 1</option>
                <option value="10 IPA 2">10 IPA 2</option>
                <option value="10 IPS 1">10 IPS 1</option>
                <option value="11 IPA 1">11 IPA 1</option>
                <option value="11 IPA 2">11 IPA 2</option>
                <option value="11 IPS 1">11 IPS 1</option>
                <option value="12 IPA 1">12 IPA 1</option>
                <option value="12 IPA 2">12 IPA 2</option>
                <option value="12 IPS 1">12 IPS 1</option>
              </select>
            </div>
          </div>

          {/* Search Results Info */}
          {(searchQuery || statusFilter || kelasFilter) && (
            <div className="mt-4 pt-4 border-t border-gray-200">
              <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-2">
                <p className="text-sm text-gray-600">
                  {totalSiswa > 0 ? (
                    <>Menampilkan {siswaList.length} dari {totalSiswa} siswa</>
                  ) : (
                    <>Tidak ada siswa yang ditemukan</>
                  )}
                  {searchQuery && (
                    <> untuk pencarian "<span className="font-medium">{searchQuery}</span>"</>
                  )}
                </p>
                <button
                  onClick={() => {
                    setSearchQuery('');
                    setStatusFilter('');
                    setKelasFilter('');
                    setCurrentPage(1);
                  }}
                  className="text-sm text-blue-600 hover:text-blue-800 font-medium"
                >
                  Reset Filter
                </button>
              </div>
            </div>
          )}
        </div>

        {siswaList.length === 0 ? (
          <div className="bg-gray-50 border border-gray-200 rounded-lg p-6 sm:p-8 text-center">
            <div className="text-4xl sm:text-6xl mb-4">👥</div>
            <p className="text-gray-500 text-base sm:text-lg">Belum ada data siswa</p>
            <p className="text-gray-400 mt-2 text-sm sm:text-base">Tambahkan siswa pertama untuk memulai</p>
          </div>
        ) : (
          <>
            {/* Desktop Table View */}
            <div className="hidden lg:block bg-white shadow-md rounded-lg overflow-hidden">
              <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Nama Lengkap
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        NIS
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Kelas
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Aksi
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {siswaList.map((siswa) => (
                      <tr key={siswa.id_siswa} className="hover:bg-gray-50">
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="text-sm font-medium text-gray-900">
                            {siswa.nama_lengkap}
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="text-sm text-gray-900">{siswa.nis}</div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <span className="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">
                            {siswa.kelas_awal || '-'}
                          </span>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                          <Link
                            to={`/siswa/${siswa.id_siswa}`}
                            className="text-blue-600 hover:text-blue-900 mr-4"
                          >
                            Detail
                          </Link>
                          <Link
                            to={`/upload-berkas/${siswa.id_siswa}`}
                            className="text-green-600 hover:text-green-900"
                          >
                            Upload Berkas
                          </Link>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>

            {/* Mobile Card View */}
            <div className="lg:hidden space-y-4">
              {siswaList.map((siswa) => (
                <div key={siswa.id_siswa} className="bg-white rounded-lg shadow-md p-4 border border-gray-200">
                  <div className="flex items-start justify-between mb-3">
                    <div className="flex-1">
                      <h3 className="text-lg font-semibold text-gray-900 mb-1">
                        {siswa.nama_lengkap}
                      </h3>
                      <div className="flex flex-wrap gap-2 text-sm text-gray-600">
                        <span className="flex items-center">
                          <span className="font-medium">NIS:</span>
                          <span className="ml-1">{siswa.nis}</span>
                        </span>
                        <span className="text-gray-400">•</span>
                        <span className="flex items-center">
                          <span className="font-medium">Kelas:</span>
                          <span className="ml-1 inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">
                            {siswa.kelas_awal || '-'}
                          </span>
                        </span>
                      </div>
                    </div>
                  </div>

                  <div className="flex flex-col sm:flex-row gap-2 sm:gap-3">
                    <Link
                      to={`/siswa/${siswa.id_siswa}`}
                      className="flex-1 text-center bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors text-sm font-medium"
                    >
                      👁️ Detail
                    </Link>
                    <Link
                      to={`/upload-berkas/${siswa.id_siswa}`}
                      className="flex-1 text-center bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors text-sm font-medium"
                    >
                      📁 Upload Berkas
                    </Link>
                  </div>
                </div>
              ))}
            </div>
          </>
        )}

        {/* Pagination */}
        {totalPages > 1 && (
          <div className="bg-white rounded-lg shadow-md p-4 mt-6">
            <div className="flex flex-col sm:flex-row justify-between items-center gap-4">
              <div className="text-sm text-gray-600">
                Halaman {currentPage} dari {totalPages} ({totalSiswa} total siswa)
              </div>

              <div className="flex items-center space-x-2">
                <button
                  onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
                  disabled={currentPage === 1}
                  className="px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  ← Sebelumnya
                </button>

                {/* Page Numbers */}
                <div className="flex space-x-1">
                  {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                    let pageNum;
                    if (totalPages <= 5) {
                      pageNum = i + 1;
                    } else if (currentPage <= 3) {
                      pageNum = i + 1;
                    } else if (currentPage >= totalPages - 2) {
                      pageNum = totalPages - 4 + i;
                    } else {
                      pageNum = currentPage - 2 + i;
                    }

                    return (
                      <button
                        key={pageNum}
                        onClick={() => setCurrentPage(pageNum)}
                        className={`px-3 py-2 text-sm font-medium rounded-md ${
                          currentPage === pageNum
                            ? 'bg-blue-600 text-white'
                            : 'text-gray-500 bg-white border border-gray-300 hover:bg-gray-50'
                        }`}
                      >
                        {pageNum}
                      </button>
                    );
                  })}
                </div>

                <button
                  onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
                  disabled={currentPage === totalPages}
                  className="px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  Selanjutnya →
                </button>
              </div>
            </div>
          </div>
        )}
      </div>
    </MainLayout>
  );
};

export default SiswaListPage;