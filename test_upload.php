<?php
/**
 * Test Upload System
 */

require_once 'app/helpers/Security.php';
require_once 'app/helpers/SimpleSessionManager.php';
require_once 'app/models/Berkas.php';
require_once 'app/models/Siswa.php';

$sessionManager = new SimpleSessionManager();

// Check authentication
if (!Security::isAuthenticated()) {
    echo "<p style='color: red;'>❌ Not authenticated. <a href='simple_login.php'>Please login first</a></p>";
    exit;
}

echo "<h2>🧪 Test Upload System</h2>";

try {
    // Test Security methods
    echo "<h3>🔒 Testing Security Methods</h3>";
    
    $token = Security::generateCSRFToken();
    echo "<p>✅ CSRF Token generated: <code>" . substr($token, 0, 16) . "...</code></p>";
    
    $isValid = Security::validateCSRFToken($token);
    echo "<p>✅ CSRF Token validation: " . ($isValid ? "Valid" : "Invalid") . "</p>";
    
    // Test Berkas model
    echo "<h3>📁 Testing Berkas Model</h3>";
    
    $berkasModel = new Berkas();
    echo "<p>✅ Berkas model instantiated</p>";
    
    $categories = $berkasModel->getFileCategories();
    echo "<p>✅ File categories loaded: " . count($categories) . " categories</p>";
    
    $allowedTypes = $berkasModel->getAllowedTypes();
    echo "<p>✅ Allowed types loaded: " . count($allowedTypes) . " types</p>";
    
    $uploadDir = $berkasModel->getUploadDir();
    echo "<p>✅ Upload directory: <code>$uploadDir</code></p>";
    echo "<p>Directory exists: " . (is_dir($uploadDir) ? "✅ Yes" : "❌ No") . "</p>";
    echo "<p>Directory writable: " . (is_writable($uploadDir) ? "✅ Yes" : "❌ No") . "</p>";
    
    // Test Siswa model
    echo "<h3>👥 Testing Siswa Model</h3>";
    
    $siswaModel = new Siswa();
    $siswa = $siswaModel->getById(1);
    
    if ($siswa) {
        echo "<p>✅ Test siswa found: " . htmlspecialchars($siswa['nama_lengkap'] ?? 'N/A') . "</p>";
        
        // Test existing files
        $existingFiles = $berkasModel->getBySiswaId(1);
        echo "<p>✅ Existing files for siswa: " . count($existingFiles) . " files</p>";
        
    } else {
        echo "<p>❌ No test siswa found with ID 1</p>";
    }
    
    echo "<h3>📋 File Categories</h3>";
    echo "<ul>";
    foreach ($categories as $category => $types) {
        echo "<li><strong>$category</strong>";
        echo "<ul>";
        foreach ($types as $key => $label) {
            echo "<li>$key: $label</li>";
        }
        echo "</ul></li>";
    }
    echo "</ul>";
    
    echo "<p style='color: green; font-weight: bold;'>🎉 All tests passed! Upload system is ready.</p>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Error: " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "<pre>" . htmlspecialchars($e->getTraceAsString()) . "</pre>";
}

echo "<hr>";
echo "<p><a href='public/upload/berkas/1'>🔗 Test Upload Page</a></p>";
echo "<p><a href='public/siswa/detail/1'>👤 Student Detail</a></p>";
echo "<p><a href='public/siswa'>📋 Student List</a></p>";
?>
