<?php
// Check if siswa data exists
if (!isset($siswa) || empty($siswa)) {
    echo '<div class="alert alert-danger">Data siswa tidak ditemukan.</div>';
    return;
}
?>

<div class="container-fluid">
    <!-- Page Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="h3 mb-0 text-gray-800">
                        <i class="bi bi-files text-primary"></i>
                        Berkas Siswa
                    </h1>
                    <p class="text-muted mb-0">
                        Dokumen untuk: <strong><?= htmlspecialchars($siswa['nama_lengkap'] ?? 'N/A') ?></strong>
                        (NIS: <?= htmlspecialchars($siswa['nis'] ?? 'N/A') ?>)
                    </p>
                </div>
                <div>
                    <a href="/siswa-app/public/upload/berkas/<?= $siswa['id_siswa'] ?? $siswa['id'] ?>" class="btn btn-primary">
                        <i class="bi bi-upload"></i>
                        Upload Berkas
                    </a>
                    <a href="/siswa-app/public/siswa/detail/<?= $siswa['id_siswa'] ?? $siswa['id'] ?>" class="btn btn-secondary">
                        <i class="bi bi-arrow-left"></i>
                        Kembali
                    </a>
                </div>
            </div>
        </div>
    </div>

    <?php if (!empty($berkas)): ?>
        <?php
        // Group berkas by category
        $berkasModel = new Berkas();
        $fileCategories = $berkasModel->getFileCategories();
        $allowedTypes = $berkasModel->getAllowedTypes();
        
        // Group files by category
        $groupedBerkas = [];
        foreach ($berkas as $b) {
            $category = null;
            foreach ($fileCategories as $catName => $types) {
                if (array_key_exists($b['jenis_berkas'], $types)) {
                    $category = $catName;
                    break;
                }
            }
            if (!$category) $category = 'Lainnya';
            $groupedBerkas[$category][] = $b;
        }
        ?>
        
        <?php foreach ($groupedBerkas as $categoryName => $categoryFiles): ?>
            <div class="row mb-4">
                <div class="col-12">
                    <div class="card shadow">
                        <div class="card-header py-3">
                            <h6 class="m-0 font-weight-bold text-primary">
                                <i class="bi bi-folder"></i>
                                <?= htmlspecialchars($categoryName) ?>
                                <span class="badge bg-primary ms-2"><?= count($categoryFiles) ?> file</span>
                            </h6>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <?php foreach ($categoryFiles as $b): ?>
                                    <div class="col-lg-4 col-md-6 mb-4">
                                        <div class="card h-100 border-0 shadow-sm">
                                            <div class="card-body">
                                                <div class="d-flex justify-content-between align-items-start mb-3">
                                                    <div class="flex-grow-1">
                                                        <h6 class="card-title mb-2">
                                                            <?= htmlspecialchars($b['nama_berkas'] ?? 'File') ?>
                                                        </h6>
                                                        <p class="card-text text-muted small mb-2">
                                                            <strong><?= $allowedTypes[$b['jenis_berkas']] ?? ucfirst(str_replace('_', ' ', $b['jenis_berkas'])) ?></strong>
                                                        </p>
                                                        <div class="text-muted small">
                                                            <div class="mb-1">
                                                                <i class="bi bi-hdd"></i>
                                                                <?= number_format(($b['ukuran_file'] ?? 0) / 1024, 1) ?> KB
                                                            </div>
                                                            <div class="mb-1">
                                                                <i class="bi bi-calendar"></i>
                                                                <?= date('d/m/Y H:i', strtotime($b['created_at'] ?? 'now')) ?>
                                                            </div>
                                                            <?php if (!empty($b['uploaded_by'])): ?>
                                                                <div class="mb-1">
                                                                    <i class="bi bi-person"></i>
                                                                    User ID: <?= $b['uploaded_by'] ?>
                                                                </div>
                                                            <?php endif; ?>
                                                        </div>
                                                    </div>
                                                    <div class="dropdown">
                                                        <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                                                            <i class="bi bi-three-dots-vertical"></i>
                                                        </button>
                                                        <ul class="dropdown-menu">
                                                            <li>
                                                                <a class="dropdown-item" href="/siswa-app/public/upload/download/<?= $b['id'] ?>">
                                                                    <i class="bi bi-download"></i> Download
                                                                </a>
                                                            </li>
                                                            <li><hr class="dropdown-divider"></li>
                                                            <li>
                                                                <a class="dropdown-item text-danger" href="/siswa-app/public/upload/delete/<?= $b['id'] ?>" 
                                                                   onclick="return confirm('Yakin ingin menghapus file ini?')">
                                                                    <i class="bi bi-trash"></i> Hapus
                                                                </a>
                                                            </li>
                                                        </ul>
                                                    </div>
                                                </div>
                                                
                                                <?php if (!empty($b['keterangan'])): ?>
                                                    <div class="border-top pt-2">
                                                        <small class="text-muted">
                                                            <i class="bi bi-chat-text"></i>
                                                            <?= htmlspecialchars($b['keterangan']) ?>
                                                        </small>
                                                    </div>
                                                <?php endif; ?>
                                                
                                                <!-- File type icon -->
                                                <div class="text-center mt-3">
                                                    <?php
                                                    $extension = pathinfo($b['nama_file_sistem'] ?? '', PATHINFO_EXTENSION);
                                                    $iconClass = 'bi-file-earmark';
                                                    $iconColor = '#6c757d';
                                                    
                                                    switch (strtolower($extension)) {
                                                        case 'pdf':
                                                            $iconClass = 'bi-file-earmark-pdf';
                                                            $iconColor = '#dc3545';
                                                            break;
                                                        case 'jpg':
                                                        case 'jpeg':
                                                        case 'png':
                                                        case 'gif':
                                                            $iconClass = 'bi-file-earmark-image';
                                                            $iconColor = '#198754';
                                                            break;
                                                        case 'doc':
                                                        case 'docx':
                                                            $iconClass = 'bi-file-earmark-word';
                                                            $iconColor = '#0d6efd';
                                                            break;
                                                    }
                                                    ?>
                                                    <i class="<?= $iconClass ?>" style="font-size: 2rem; color: <?= $iconColor ?>;"></i>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        <?php endforeach; ?>
        
    <?php else: ?>
        <div class="row">
            <div class="col-12">
                <div class="card shadow">
                    <div class="card-body text-center py-5">
                        <i class="bi bi-file-earmark" style="font-size: 4rem; color: #dee2e6;"></i>
                        <h4 class="text-muted mt-3 mb-2">Belum ada berkas</h4>
                        <p class="text-muted mb-4">Upload berkas pertama untuk siswa ini</p>
                        <a href="/siswa-app/public/upload/berkas/<?= $siswa['id_siswa'] ?? $siswa['id'] ?>" class="btn btn-primary btn-lg">
                            <i class="bi bi-upload"></i>
                            Upload Berkas Sekarang
                        </a>
                    </div>
                </div>
            </div>
        </div>
    <?php endif; ?>
</div>

<style>
.card:hover {
    transform: translateY(-2px);
    transition: transform 0.2s ease-in-out;
}

.dropdown-menu {
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}
</style>
