<div class="container">
    <div class="row justify-content-center">
        <div class="col-md-6 col-lg-4">
            <div class="card shadow-lg border-0 mt-5">
                <div class="card-header bg-primary text-white text-center py-4">
                    <img src="/assets/images/logo.png" alt="Logo" width="60" height="60" class="mb-3">
                    <h4 class="mb-0 fw-bold">SISWA APP</h4>
                    <small>Sistem Informasi Akademik Siswa</small>
                </div>
                
                <div class="card-body p-4">
                    <h5 class="card-title text-center mb-4">Masuk ke Akun Anda</h5>
                    
                    <?php if (isset($error)): ?>
                    <div class="alert alert-danger alert-dismissible fade show" role="alert">
                        <i class="bi bi-exclamation-triangle-fill"></i>
                        <?= htmlspecialchars($error) ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                    <?php endif; ?>
                    
                    <form method="POST" action="auth/login" id="loginForm" novalidate>
                        <input type="hidden" name="csrf_token" value="<?= $csrf_token ?>">
                        
                        <div class="mb-3">
                            <label for="username" class="form-label">
                                <i class="bi bi-person"></i> Username
                            </label>
                            <input type="text" 
                                   class="form-control form-control-lg" 
                                   id="username" 
                                   name="username" 
                                   placeholder="Masukkan username"
                                   required 
                                   autocomplete="username"
                                   value="<?= isset($_POST['username']) ? htmlspecialchars($_POST['username']) : '' ?>">
                            <div class="invalid-feedback">
                                Username wajib diisi.
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="password" class="form-label">
                                <i class="bi bi-lock"></i> Password
                            </label>
                            <div class="input-group">
                                <input type="password" 
                                       class="form-control form-control-lg" 
                                       id="password" 
                                       name="password" 
                                       placeholder="Masukkan password"
                                       required 
                                       autocomplete="current-password">
                                <button class="btn btn-outline-secondary" type="button" id="togglePassword">
                                    <i class="bi bi-eye" id="toggleIcon"></i>
                                </button>
                            </div>
                            <div class="invalid-feedback">
                                Password wajib diisi.
                            </div>
                        </div>
                        
                        <div class="mb-3 form-check">
                            <input type="checkbox" class="form-check-input" id="remember" name="remember">
                            <label class="form-check-label" for="remember">
                                Ingat saya selama 30 hari
                            </label>
                        </div>
                        
                        <div class="d-grid">
                            <button type="submit" class="btn btn-primary btn-lg" id="loginBtn">
                                <i class="bi bi-box-arrow-in-right"></i> Masuk
                            </button>
                        </div>
                    </form>
                </div>
                
                <div class="card-footer text-center py-3 bg-light">
                    <small class="text-muted">
                        <i class="bi bi-shield-check"></i>
                        Sistem dilindungi dengan enkripsi SSL
                    </small>
                </div>
            </div>
            
            <!-- Demo Credentials -->
            <div class="card mt-3 border-info">
                <div class="card-body text-center">
                    <h6 class="card-title text-info">
                        <i class="bi bi-info-circle"></i> Demo Credentials
                    </h6>
                    <p class="card-text small mb-2">
                        <strong>Username:</strong> admin<br>
                        <strong>Password:</strong> password
                    </p>
                    <button type="button" class="btn btn-sm btn-outline-info" onclick="fillDemoCredentials()">
                        Gunakan Demo
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Form validation
(function() {
    'use strict';
    
    const form = document.getElementById('loginForm');
    const loginBtn = document.getElementById('loginBtn');
    
    form.addEventListener('submit', function(event) {
        if (!form.checkValidity()) {
            event.preventDefault();
            event.stopPropagation();
        } else {
            // Show loading state
            loginBtn.innerHTML = '<span class="spinner-border spinner-border-sm me-2"></span>Memproses...';
            loginBtn.disabled = true;
        }
        
        form.classList.add('was-validated');
    });
    
    // Password toggle
    const togglePassword = document.getElementById('togglePassword');
    const passwordField = document.getElementById('password');
    const toggleIcon = document.getElementById('toggleIcon');
    
    togglePassword.addEventListener('click', function() {
        const type = passwordField.getAttribute('type') === 'password' ? 'text' : 'password';
        passwordField.setAttribute('type', type);
        
        if (type === 'password') {
            toggleIcon.className = 'bi bi-eye';
        } else {
            toggleIcon.className = 'bi bi-eye-slash';
        }
    });
    
    // Real-time validation
    const inputs = form.querySelectorAll('input[required]');
    inputs.forEach(input => {
        input.addEventListener('blur', function() {
            if (this.value.trim() === '') {
                this.classList.add('is-invalid');
            } else {
                this.classList.remove('is-invalid');
                this.classList.add('is-valid');
            }
        });
        
        input.addEventListener('input', function() {
            if (this.classList.contains('is-invalid') && this.value.trim() !== '') {
                this.classList.remove('is-invalid');
                this.classList.add('is-valid');
            }
        });
    });
})();

// Fill demo credentials
function fillDemoCredentials() {
    document.getElementById('username').value = 'admin';
    document.getElementById('password').value = 'password';
    
    // Trigger validation
    document.getElementById('username').classList.add('is-valid');
    document.getElementById('password').classList.add('is-valid');
}

// Security: Prevent multiple form submissions
let formSubmitted = false;
document.getElementById('loginForm').addEventListener('submit', function() {
    if (formSubmitted) {
        return false;
    }
    formSubmitted = true;
});

// Auto-focus on username field
document.addEventListener('DOMContentLoaded', function() {
    document.getElementById('username').focus();
});
</script>

<style>
.card {
    border-radius: 15px;
}

.card-header {
    border-radius: 15px 15px 0 0 !important;
}

.form-control:focus {
    border-color: #0d6efd;
    box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
}

.btn-primary {
    background: linear-gradient(45deg, #0d6efd, #0b5ed7);
    border: none;
}

.btn-primary:hover {
    background: linear-gradient(45deg, #0b5ed7, #0a58ca);
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.2);
}

.alert {
    border-radius: 10px;
}

.input-group .btn {
    border-left: none;
}

.form-check-input:checked {
    background-color: #0d6efd;
    border-color: #0d6efd;
}

/* Loading animation */
.spinner-border-sm {
    width: 1rem;
    height: 1rem;
}

/* Demo card styling */
.border-info {
    border-color: #0dcaf0 !important;
}

/* Responsive adjustments */
@media (max-width: 576px) {
    .card {
        margin: 1rem;
    }
    
    .card-body {
        padding: 2rem 1.5rem;
    }
}
</style>
