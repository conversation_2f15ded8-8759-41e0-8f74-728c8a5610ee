# Siswa App - Apache Configuration
# Disable directory browsing
Options -Indexes

# Set default index files
DirectoryIndex public/index.php index.php

# Enable URL rewriting
RewriteEngine On

# Redirect root to public directory
RewriteCond %{REQUEST_URI} !^/public/
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteRule ^(.*)$ public/$1 [L]

# Handle requests in public directory
RewriteCond %{REQUEST_URI} ^/public/
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteRule ^public/(.*)$ public/index.php [L]

# Security headers
<IfModule mod_headers.c>
    Header always set X-Content-Type-Options nosniff
    Header always set X-Frame-Options DENY
    Header always set X-XSS-Protection "1; mode=block"
    Header always set Referrer-Policy "strict-origin-when-cross-origin"
    Header always set Permissions-Policy "geolocation=(), microphone=(), camera=()"
</IfModule>

# Prevent access to sensitive files
<FilesMatch "\.(env|log|sql|md|json|lock)$">
    Order allow,deny
    Deny from all
</FilesMatch>

# Prevent access to directories
RedirectMatch 404 /\.git
RedirectMatch 404 /vendor
RedirectMatch 404 /logs
RedirectMatch 404 /database