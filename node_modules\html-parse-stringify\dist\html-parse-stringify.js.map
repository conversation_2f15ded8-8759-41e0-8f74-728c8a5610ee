{"version": 3, "file": "html-parse-stringify.js", "sources": ["../src/parse-tag.js", "../src/parse.js", "../src/stringify.js", "../src/index.js"], "sourcesContent": ["import lookup from 'void-elements'\nconst attrRE = /\\s([^'\"/\\s><]+?)[\\s/>]|([^\\s=]+)=\\s?(\".*?\"|'.*?')/g\n\nexport default function stringify(tag) {\n  const res = {\n    type: 'tag',\n    name: '',\n    voidElement: false,\n    attrs: {},\n    children: [],\n  }\n\n  const tagMatch = tag.match(/<\\/?([^\\s]+?)[/\\s>]/)\n  if (tagMatch) {\n    res.name = tagMatch[1]\n    if (\n      lookup[tagMatch[1]] ||\n      tag.charAt(tag.length - 2) === '/'\n    ) {\n      res.voidElement = true\n    }\n\n    // handle comment tag\n    if (res.name.startsWith('!--')) {\n      const endIndex = tag.indexOf('-->')\n      return {\n        type: 'comment',\n        comment: endIndex !== -1 ? tag.slice(4, endIndex) : '',\n      }\n    }\n  }\n\n  const reg = new RegExp(attrRE)\n  let result = null\n  for (;;) {\n    result = reg.exec(tag)\n\n    if (result === null) {\n      break\n    }\n\n    if (!result[0].trim()) {\n      continue\n    }\n\n    if (result[1]) {\n      const attr = result[1].trim()\n      let arr = [attr, '']\n\n      if (attr.indexOf('=') > -1) {\n        arr = attr.split('=')\n      }\n\n      res.attrs[arr[0]] = arr[1]\n      reg.lastIndex--\n    } else if (result[2]) {\n      res.attrs[result[2]] = result[3].trim().substring(1, result[3].length - 1)\n    }\n  }\n\n  return res\n}\n", "import parseTag from './parse-tag'\n\nconst tagRE = /<[a-zA-Z0-9\\-\\!\\/](?:\"[^\"]*\"|'[^']*'|[^'\">])*>/g\nconst whitespaceRE = /^\\s*$/\n\n// re-used obj for quick lookups of components\nconst empty = Object.create(null)\n\nexport default function parse(html, options) {\n  options || (options = {})\n  options.components || (options.components = empty)\n  const result = []\n  const arr = []\n  let current\n  let level = -1\n  let inComponent = false\n\n  // handle text at top level\n  if (html.indexOf('<') !== 0) {\n    var end = html.indexOf('<')\n    result.push({\n      type: 'text',\n      content: end === -1 ? html : html.substring(0, end),\n    })\n  }\n\n  html.replace(tagRE, function (tag, index) {\n    if (inComponent) {\n      if (tag !== '</' + current.name + '>') {\n        return\n      } else {\n        inComponent = false\n      }\n    }\n    const isOpen = tag.charAt(1) !== '/'\n    const isComment = tag.startsWith('<!--')\n    const start = index + tag.length\n    const nextChar = html.charAt(start)\n    let parent\n\n    if (isComment) {\n      const comment = parseTag(tag)\n\n      // if we're at root, push new base node\n      if (level < 0) {\n        result.push(comment)\n        return result\n      }\n      parent = arr[level]\n      parent.children.push(comment)\n      return result\n    }\n\n    if (isOpen) {\n      level++\n\n      current = parseTag(tag)\n      if (current.type === 'tag' && options.components[current.name]) {\n        current.type = 'component'\n        inComponent = true\n      }\n\n      if (\n        !current.voidElement &&\n        !inComponent &&\n        nextChar &&\n        nextChar !== '<'\n      ) {\n        current.children.push({\n          type: 'text',\n          content: html.slice(start, html.indexOf('<', start)),\n        })\n      }\n\n      // if we're at root, push new base node\n      if (level === 0) {\n        result.push(current)\n      }\n\n      parent = arr[level - 1]\n\n      if (parent) {\n        parent.children.push(current)\n      }\n\n      arr[level] = current\n    }\n\n    if (!isOpen || current.voidElement) {\n      if (\n        level > -1 &&\n        (current.voidElement || current.name === tag.slice(2, -1))\n      ) {\n        level--\n        // move current up a level to match the end tag\n        current = level === -1 ? result : arr[level]\n      }\n      if (!inComponent && nextChar !== '<' && nextChar) {\n        // trailing text node\n        // if we're at the root, push a base text node. otherwise add as\n        // a child to the current node.\n        parent = level === -1 ? result : arr[level].children\n\n        // calculate correct end of the content slice in case there's\n        // no tag after the text node.\n        const end = html.indexOf('<', start)\n        let content = html.slice(start, end === -1 ? undefined : end)\n        // if a node is nothing but whitespace, collapse it as the spec states:\n        // https://www.w3.org/TR/html4/struct/text.html#h-9.1\n        if (whitespaceRE.test(content)) {\n          content = ' '\n        }\n        // don't add whitespace-only text nodes if they would be trailing text nodes\n        // or if they would be leading whitespace-only text nodes:\n        //  * end > -1 indicates this is not a trailing text node\n        //  * leading node is when level is -1 and parent has length 0\n        if ((end > -1 && level + parent.length >= 0) || content !== ' ') {\n          parent.push({\n            type: 'text',\n            content: content,\n          })\n        }\n      }\n    }\n  })\n\n  return result\n}\n", "function attrString(attrs) {\n  const buff = []\n  for (let key in attrs) {\n    buff.push(key + '=\"' + attrs[key] + '\"')\n  }\n  if (!buff.length) {\n    return ''\n  }\n  return ' ' + buff.join(' ')\n}\n\nfunction stringify(buff, doc) {\n  switch (doc.type) {\n    case 'text':\n      return buff + doc.content\n    case 'tag':\n      buff +=\n        '<' +\n        doc.name +\n        (doc.attrs ? attrString(doc.attrs) : '') +\n        (doc.voidElement ? '/>' : '>')\n      if (doc.voidElement) {\n        return buff\n      }\n      return buff + doc.children.reduce(stringify, '') + '</' + doc.name + '>'\n    case 'comment':\n      buff += '<!--' + doc.comment + '-->'\n      return buff\n  }\n}\n\nexport default function (doc) {\n  return doc.reduce(function (token, rootEl) {\n    return token + stringify('', rootEl)\n  }, '')\n}\n", "import parse from './parse'\nimport stringify from './stringify'\n\nexport default {\n  parse,\n  stringify,\n}\n"], "names": ["attrRE", "stringify", "tag", "res", "type", "name", "voidElement", "attrs", "children", "tagMatch", "match", "lookup", "char<PERSON>t", "length", "startsWith", "endIndex", "indexOf", "comment", "slice", "reg", "RegExp", "result", "exec", "trim", "attr", "arr", "split", "lastIndex", "substring", "tagRE", "whitespaceRE", "empty", "Object", "create", "buff", "doc", "content", "key", "push", "join", "attrString", "reduce", "parse", "html", "options", "components", "current", "level", "inComponent", "end", "replace", "index", "parent", "isOpen", "isComment", "start", "nextChar", "parseTag", "undefined", "test", "token", "rootEl"], "mappings": "oFACMA,EAAS,8DAESC,EAAUC,GAChC,IAAMC,EAAM,CACVC,KAAM,MACNC,KAAM,GACNC,aAAa,EACbC,MAAO,GACPC,SAAU,IAGNC,EAAWP,EAAIQ,MAAM,uBAC3B,GAAID,IACFN,EAAIE,KAAOI,EAAS,IAElBE,EAAOF,EAAS,KACe,MAA/BP,EAAIU,OAAOV,EAAIW,OAAS,MAExBV,EAAIG,aAAc,GAIhBH,EAAIE,KAAKS,WAAW,QAAQ,CAC9B,IAAMC,EAAWb,EAAIc,QAAQ,UAC7B,MAAO,CACLZ,KAAM,UACNa,SAAuB,IAAdF,EAAkBb,EAAIgB,MAAM,EAAGH,GAAY,IAO1D,IAFA,IAAMI,EAAM,IAAIC,OAAOpB,GACnBqB,EAAS,KAII,QAFfA,EAASF,EAAIG,KAAKpB,KAMlB,GAAKmB,EAAO,GAAGE,OAIf,GAAIF,EAAO,GAAI,CACb,IAAMG,EAAOH,EAAO,GAAGE,OACnBE,EAAM,CAACD,EAAM,IAEbA,EAAKR,QAAQ,MAAQ,IACvBS,EAAMD,EAAKE,MAAM,MAGnBvB,EAAII,MAAMkB,EAAI,IAAMA,EAAI,GACxBN,EAAIQ,iBACKN,EAAO,KAChBlB,EAAII,MAAMc,EAAO,IAAMA,EAAO,GAAGE,OAAOK,UAAU,EAAGP,EAAO,GAAGR,OAAS,IAI5E,OAAOV,EC1DT,IAAM0B,EAAQ,kDACRC,EAAe,QAGfC,EAAQC,OAAOC,OAAO,MCK5B,SAAShC,EAAUiC,EAAMC,GACvB,OAAQA,EAAI/B,MACV,IAAK,OACH,OAAO8B,EAAOC,EAAIC,QACpB,IAAK,MAMH,OALAF,GACE,IACAC,EAAI9B,MACH8B,EAAI5B,MAnBb,SAAoBA,GAClB,IAAM2B,EAAO,GACb,IAAK,IAAIG,KAAO9B,EACd2B,EAAKI,KAAKD,EAAM,KAAO9B,EAAM8B,GAAO,KAEtC,OAAKH,EAAKrB,OAGH,IAAMqB,EAAKK,KAAK,KAFd,GAaUC,CAAWL,EAAI5B,OAAS,KACpC4B,EAAI7B,YAAc,KAAO,KACxB6B,EAAI7B,YACC4B,EAEFA,EAAOC,EAAI3B,SAASiC,OAAOxC,EAAW,IAAM,KAAOkC,EAAI9B,KAAO,IACvE,IAAK,UAEH,OADA6B,EAAQ,UAASC,EAAIlB,QAAU,yBCvBtB,CACbyB,MFIF,SAA8BC,EAAMC,GAClCA,IAAYA,EAAU,IACtBA,EAAQC,aAAeD,EAAQC,WAAad,GAC5C,IAEIe,EAFEzB,EAAS,GACTI,EAAM,GAERsB,GAAS,EACTC,GAAc,EAGlB,GAA0B,IAAtBL,EAAK3B,QAAQ,KAAY,CAC3B,IAAIiC,EAAMN,EAAK3B,QAAQ,KACvBK,EAAOiB,KAAK,CACVlC,KAAM,OACNgC,SAAkB,IAATa,EAAaN,EAAOA,EAAKf,UAAU,EAAGqB,KAwGnD,OApGAN,EAAKO,QAAQrB,EAAO,SAAU3B,EAAKiD,GACjC,GAAIH,EAAa,CACf,GAAI9C,IAAQ,KAAO4C,EAAQzC,KAAO,IAChC,OAEA2C,GAAc,EAGlB,IAIII,EAJEC,EAA2B,MAAlBnD,EAAIU,OAAO,GACpB0C,EAAYpD,EAAIY,WAAW,WAC3ByC,EAAQJ,EAAQjD,EAAIW,OACpB2C,EAAWb,EAAK/B,OAAO2C,GAG7B,GAAID,EAAW,CACb,IAAMrC,EAAUwC,EAASvD,GAGzB,OAAI6C,EAAQ,GACV1B,EAAOiB,KAAKrB,GACLI,KAET+B,EAAS3B,EAAIsB,IACNvC,SAAS8B,KAAKrB,GACdI,GAsCT,GAnCIgC,IACFN,IAGqB,SADrBD,EAAUW,EAASvD,IACPE,MAAkBwC,EAAQC,WAAWC,EAAQzC,QACvDyC,EAAQ1C,KAAO,YACf4C,GAAc,GAIbF,EAAQxC,aACR0C,IACDQ,GACa,MAAbA,GAEAV,EAAQtC,SAAS8B,KAAK,CACpBlC,KAAM,OACNgC,QAASO,EAAKzB,MAAMqC,EAAOZ,EAAK3B,QAAQ,IAAKuC,MAKnC,IAAVR,GACF1B,EAAOiB,KAAKQ,IAGdM,EAAS3B,EAAIsB,EAAQ,KAGnBK,EAAO5C,SAAS8B,KAAKQ,GAGvBrB,EAAIsB,GAASD,KAGVO,GAAUP,EAAQxC,eAEnByC,GAAS,IACRD,EAAQxC,aAAewC,EAAQzC,OAASH,EAAIgB,MAAM,GAAI,MAEvD6B,IAEAD,GAAqB,IAAXC,EAAe1B,EAASI,EAAIsB,KAEnCC,GAA4B,MAAbQ,GAAoBA,GAAU,CAIhDJ,GAAoB,IAAXL,EAAe1B,EAASI,EAAIsB,GAAOvC,SAI5C,IAAMyC,EAAMN,EAAK3B,QAAQ,IAAKuC,GAC1BnB,EAAUO,EAAKzB,MAAMqC,GAAgB,IAATN,OAAaS,EAAYT,GAGrDnB,EAAa6B,KAAKvB,KACpBA,EAAU,MAMPa,GAAO,GAAKF,EAAQK,EAAOvC,QAAU,GAAkB,MAAZuB,IAC9CgB,EAAOd,KAAK,CACVlC,KAAM,OACNgC,QAASA,OAOZf,GEzHPpB,mBD0BuBkC,GACvB,OAAOA,EAAIM,OAAO,SAAUmB,EAAOC,GACjC,OAAOD,EAAQ3D,EAAU,GAAI4D,IAC5B"}