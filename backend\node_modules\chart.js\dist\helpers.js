/*!
 * Chart.js v4.4.9
 * https://www.chartjs.org
 * (c) 2025 Chart.js Contributors
 * Released under the MIT License
 */
export { H as HALF_PI, b3 as INFINITY, P as PI, b2 as PITAU, b5 as QUARTER_PI, b4 as RAD_PER_DEG, T as TAU, b6 as TWO_THIRDS_PI, R as _addGrace, X as _alignPixel, a2 as _alignStartEnd, p as _angleBetween, b7 as _angleDiff, _ as _arrayUnique, a8 as _attachContext, at as _bezierCurveTo, aq as _bezierInterpolation, ay as _boundSegment, ao as _boundSegments, a5 as _capitalize, an as _computeSegments, a9 as _createResolver, aL as _decimalPlaces, aW as _deprecated, aa as _descriptors, ai as _elementsEqual, N as _factorize, aP as _filterBetween, I as _getParentNode, q as _getStartAndCountOfVisiblePoints, W as _int16Range, ak as _isBetween, aj as _isClickEvent, M as _isDomSupported, C as _isPointInArea, S as _limitValue, aO as _longestText, aQ as _lookup, B as _lookupByKey, V as _measureText, aU as _merger, aV as _mergerIf, az as _normalizeAngle, y as _parseObjectDataRadialScale, ar as _pointInLine, al as _readValueToProps, A as _rlookupByKey, w as _scaleRangesChanged, aH as _setMinAndMaxByKey, aX as _splitKey, ap as _steppedInterpolation, as as _steppedLineTo, aC as _textX, a1 as _toLeftRightCenter, am as _updateBezierControlPoints, av as addRoundedRectPath, aK as almostEquals, aJ as almostWhole, Q as callback, af as clearCanvas, Y as clipArea, aT as clone, c as color, j as createContext, ad as debounce, h as defined, aF as distanceBetweenPoints, au as drawPoint, aE as drawPointLegend, F as each, e as easingEffects, O as finiteOrDefault, b0 as fontString, o as formatNumber, D as getAngleFromPoint, ah as getDatasetClipArea, aS as getHoverColor, G as getMaximumSize, z as getRelativePosition, aA as getRtlAdapter, a$ as getStyle, b as isArray, g as isFinite, a7 as isFunction, k as isNullOrUndef, x as isNumber, i as isObject, aR as isPatternOrGradient, l as listenArrayEvents, aN as log10, a4 as merge, ab as mergeIf, aI as niceNum, aG as noop, aB as overrideTextDirection, J as readUsedSize, Z as renderText, r as requestAnimFrame, a as resolve, f as resolveObjectKey, aD as restoreTextDirection, ae as retinaScale, ag as setsEqual, s as sign, aZ as splineCurve, a_ as splineCurveMonotone, K as supportsEventListenerOptions, L as throttled, U as toDegrees, n as toDimension, a0 as toFont, aY as toFontString, b1 as toLineHeight, E as toPadding, m as toPercentage, t as toRadians, aw as toTRBL, ax as toTRBLCorners, ac as uid, $ as unclipArea, u as unlistenArrayEvents, v as valueOrDefault } from './chunks/helpers.dataset.js';
import '@kurkle/color';
//# sourceMappingURL=helpers.js.map
