import React, { useState } from 'react';

const FilePreview = ({ file, onClose, onDownload, onDelete }) => {
  const [loading, setLoading] = useState(false);

  const getFileIcon = (fileName) => {
    const extension = fileName.split('.').pop().toLowerCase();
    switch (extension) {
      case 'pdf':
        return '📄';
      case 'doc':
      case 'docx':
        return '📝';
      case 'xls':
      case 'xlsx':
        return '📊';
      case 'jpg':
      case 'jpeg':
      case 'png':
      case 'gif':
        return '🖼️';
      case 'zip':
      case 'rar':
        return '📦';
      default:
        return '📁';
    }
  };

  const formatFileSize = (bytes) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('id-ID', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const isImage = (fileName) => {
    const extension = fileName.split('.').pop().toLowerCase();
    return ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp'].includes(extension);
  };

  const isPDF = (fileName) => {
    return fileName.toLowerCase().endsWith('.pdf');
  };

  const handleDownload = async () => {
    setLoading(true);
    try {
      await onDownload(file);
    } catch (error) {
      console.error('Download error:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleDelete = async () => {
    if (window.confirm('Apakah Anda yakin ingin menghapus file ini?')) {
      setLoading(true);
      try {
        await onDelete(file);
        onClose();
      } catch (error) {
        console.error('Delete error:', error);
      } finally {
        setLoading(false);
      }
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-hidden">
        {/* Header */}
        <div className="flex items-center justify-between p-4 border-b border-gray-200">
          <div className="flex items-center space-x-3">
            <span className="text-2xl">{getFileIcon(file.nama_file)}</span>
            <div>
              <h3 className="text-lg font-semibold text-gray-900">{file.nama_file}</h3>
              <p className="text-sm text-gray-500">
                {formatFileSize(file.ukuran_file)} • {formatDate(file.tanggal_upload)}
              </p>
            </div>
          </div>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 p-2"
          >
            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        {/* Content */}
        <div className="p-4 max-h-[60vh] overflow-auto">
          {isImage(file.nama_file) ? (
            <div className="text-center">
              <img
                src={file.url || `/uploads/${file.nama_file}`}
                alt={file.nama_file}
                className="max-w-full max-h-96 mx-auto rounded-lg shadow-md"
                onError={(e) => {
                  e.target.style.display = 'none';
                  e.target.nextSibling.style.display = 'block';
                }}
              />
              <div className="hidden text-gray-500 mt-4">
                <span className="text-4xl">🖼️</span>
                <p className="mt-2">Gambar tidak dapat ditampilkan</p>
              </div>
            </div>
          ) : isPDF(file.nama_file) ? (
            <div className="text-center">
              <iframe
                src={file.url || `/uploads/${file.nama_file}`}
                className="w-full h-96 border rounded-lg"
                title={file.nama_file}
              />
              <p className="text-sm text-gray-500 mt-2">
                Jika PDF tidak tampil, silakan download untuk melihat file
              </p>
            </div>
          ) : (
            <div className="text-center py-12">
              <span className="text-6xl">{getFileIcon(file.nama_file)}</span>
              <h4 className="text-lg font-medium text-gray-900 mt-4">{file.nama_file}</h4>
              <p className="text-gray-500 mt-2">
                Preview tidak tersedia untuk jenis file ini
              </p>
              <p className="text-sm text-gray-400 mt-1">
                Silakan download untuk melihat file
              </p>
            </div>
          )}
        </div>

        {/* File Info */}
        <div className="px-4 py-3 bg-gray-50 border-t border-gray-200">
          <div className="grid grid-cols-1 sm:grid-cols-3 gap-4 text-sm">
            <div>
              <span className="font-medium text-gray-700">Jenis Berkas:</span>
              <p className="text-gray-600 capitalize">{file.jenis_berkas?.replace('_', ' ')}</p>
            </div>
            <div>
              <span className="font-medium text-gray-700">Ukuran:</span>
              <p className="text-gray-600">{formatFileSize(file.ukuran_file)}</p>
            </div>
            <div>
              <span className="font-medium text-gray-700">Diupload:</span>
              <p className="text-gray-600">{formatDate(file.tanggal_upload)}</p>
            </div>
          </div>
          {file.keterangan && (
            <div className="mt-3">
              <span className="font-medium text-gray-700">Keterangan:</span>
              <p className="text-gray-600 mt-1">{file.keterangan}</p>
            </div>
          )}
        </div>

        {/* Actions */}
        <div className="flex items-center justify-between p-4 border-t border-gray-200">
          <div className="flex space-x-3">
            <button
              onClick={handleDownload}
              disabled={loading}
              className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"
            >
              {loading ? (
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
              ) : (
                <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                </svg>
              )}
              Download
            </button>
            
            {onDelete && (
              <button
                onClick={handleDelete}
                disabled={loading}
                className="inline-flex items-center px-4 py-2 border border-red-300 text-sm font-medium rounded-md text-red-700 bg-white hover:bg-red-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 disabled:opacity-50"
              >
                <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                </svg>
                Hapus
              </button>
            )}
          </div>
          
          <button
            onClick={onClose}
            className="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
          >
            Tutup
          </button>
        </div>
      </div>
    </div>
  );
};

export default FilePreview;
