<?php
/**
 * Create Upload Folders Script
 * Membuat folder upload yang diperlukan
 */

echo "<h2>🗂️ Create Upload Folders</h2>";

$folders = [
    'uploads',
    'uploads/berkas',
    'uploads/temp',
    'logs'
];

foreach ($folders as $folder) {
    $fullPath = __DIR__ . '/' . $folder;
    
    if (!is_dir($fullPath)) {
        if (mkdir($fullPath, 0755, true)) {
            echo "<p style='color: green;'>✅ Created folder: <strong>$folder</strong></p>";
        } else {
            echo "<p style='color: red;'>❌ Failed to create folder: <strong>$folder</strong></p>";
        }
    } else {
        echo "<p style='color: blue;'>ℹ️ Folder already exists: <strong>$folder</strong></p>";
    }
}

// Create .htaccess for uploads folder security
$htaccessContent = "# Deny direct access to uploaded files
Options -Indexes
<Files *.php>
    Deny from all
</Files>

# Allow only specific file types
<FilesMatch \"\\.(pdf|jpg|jpeg|png|gif|doc|docx)$\">
    Allow from all
</FilesMatch>";

$htaccessPath = __DIR__ . '/uploads/.htaccess';
if (!file_exists($htaccessPath)) {
    if (file_put_contents($htaccessPath, $htaccessContent)) {
        echo "<p style='color: green;'>✅ Created .htaccess for upload security</p>";
    } else {
        echo "<p style='color: red;'>❌ Failed to create .htaccess</p>";
    }
} else {
    echo "<p style='color: blue;'>ℹ️ .htaccess already exists</p>";
}

echo "<p style='color: green; font-weight: bold;'>🎉 Upload folders setup completed!</p>";
echo "<hr>";
echo "<p><a href='public/'>🚀 Go to Application</a></p>";
?>
