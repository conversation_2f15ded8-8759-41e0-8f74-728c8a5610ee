<?php
require_once __DIR__ . '/Database.php';

class Siswa {
    private $db;

    public function __construct() {
        $this->db = new Database();
    }

    public function getAll() {
        try {
            return $this->db->fetchAll("
                SELECT s.*, k.nama_kelas
                FROM siswa s
                LEFT JOIN kelas k ON s.kelas_id = k.id
                ORDER BY s.nama_lengkap
            ");
        } catch (Exception $e) {
            // Fallback jika tabel belum ada atau error
            return [];
        }
    }

    public function getById($id) {
        try {
            return $this->db->fetch("
                SELECT s.*, k.nama_kelas
                FROM siswa s
                LEFT JOIN kelas k ON s.kelas_id = k.id
                WHERE s.id_siswa = ?
            ", [$id]);
        } catch (Exception $e) {
            return null;
        }
    }

    public function create($data) {
        $sql = "INSERT INTO siswa (nis, nama_lengkap, jenis_kelamin, kelas_id, tahun_masuk, created_by)
                VALUES (?, ?, ?, ?, ?, ?)";
        $params = [
            $data['nis'],
            $data['nama_lengkap'],
            $data['jenis_kelamin'],
            $data['kelas_id'],
            $data['tahun_masuk'],
            $data['created_by'] ?? 1
        ];
        $this->db->query($sql, $params);
        return $this->db->lastInsertId();
    }

    public function update($id, $data) {
        $sql = "UPDATE siswa SET
                nis = ?, nama_lengkap = ?, jenis_kelamin = ?,
                kelas_id = ?, tahun_masuk = ?, updated_by = ?
                WHERE id_siswa = ?";
        $params = [
            $data['nis'],
            $data['nama_lengkap'],
            $data['jenis_kelamin'],
            $data['kelas_id'],
            $data['tahun_masuk'],
            $data['updated_by'] ?? 1,
            $id
        ];
        $this->db->query($sql, $params);
        return true;
    }

    public function delete($id) {
        $this->db->query("DELETE FROM siswa WHERE id_siswa = ?", [$id]);
        return true;
    }

    public function getBySiswaId($siswaId) {
        return $this->getById($siswaId);
    }
}
?>