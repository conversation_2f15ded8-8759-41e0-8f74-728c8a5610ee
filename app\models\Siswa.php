<?php
require_once __DIR__ . '/Database.php';

class Siswa {
    private $db;

    public function __construct() {
        $this->db = new Database();
    }

    public function getAll() {
        return $this->db->fetchAll("SELECT s.*, k.nama_kelas FROM siswa s JOIN kelas k ON s.kelas_id = k.id");
    }

    public function getById($id) {
        return $this->db->fetch("SELECT s.*, k.nama_kelas FROM siswa s JOIN kelas k ON s.kelas_id = k.id WHERE s.id = ?", [$id]);
    }

    public function create($nama, $kelas_id) {
        $this->db->query("INSERT INTO siswa (nama, kelas_id) VALUES (?, ?)", [$nama, $kelas_id]);
        return $this->db->lastInsertId();
    }

    public function update($id, $nama, $kelas_id) {
        $this->db->query("UPDATE siswa SET nama = ?, kelas_id = ? WHERE id = ?", [$nama, $kelas_id, $id]);
        return true;
    }

    public function delete($id) {
        $this->db->query("DELETE FROM siswa WHERE id = ?", [$id]);
        return true;
    }
}
?>