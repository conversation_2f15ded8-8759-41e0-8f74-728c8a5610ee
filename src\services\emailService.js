// Email notification service (mock implementation)

class EmailService {
  constructor() {
    this.isEnabled = true;
    this.templates = {
      welcome: {
        subject: 'Selamat Datang di Sistem Informasi Akademik',
        template: 'welcome'
      },
      studentRegistered: {
        subject: 'Siswa Baru Terdaftar',
        template: 'student_registered'
      },
      fileUploaded: {
        subject: 'Berkas Baru Diupload',
        template: 'file_uploaded'
      },
      statusChanged: {
        subject: 'Status Siswa Berubah',
        template: 'status_changed'
      },
      systemMaintenance: {
        subject: 'Pemberitahuan Maintenance Sistem',
        template: 'system_maintenance'
      }
    };
  }

  // Mock email sending function
  async sendEmail(to, templateKey, data = {}) {
    if (!this.isEnabled) {
      console.log('Email service is disabled');
      return { success: false, error: 'Email service disabled' };
    }

    const template = this.templates[templateKey];
    if (!template) {
      console.error('Email template not found:', templateKey);
      return { success: false, error: 'Template not found' };
    }

    // Simulate email sending delay
    await new Promise(resolve => setTimeout(resolve, 1000));

    // Mock success/failure (90% success rate)
    const success = Math.random() > 0.1;

    if (success) {
      console.log(`📧 Email sent successfully to ${to}:`, {
        subject: template.subject,
        template: template.template,
        data
      });
      
      return {
        success: true,
        messageId: `msg_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        timestamp: new Date().toISOString()
      };
    } else {
      console.error(`❌ Failed to send email to ${to}`);
      return {
        success: false,
        error: 'SMTP connection failed'
      };
    }
  }

  // Send welcome email to new users
  async sendWelcomeEmail(userEmail, userName) {
    return this.sendEmail(userEmail, 'welcome', {
      userName,
      loginUrl: `${window.location.origin}/login`,
      supportEmail: '<EMAIL>'
    });
  }

  // Send notification when new student is registered
  async sendStudentRegisteredEmail(adminEmails, studentData) {
    const promises = adminEmails.map(email => 
      this.sendEmail(email, 'studentRegistered', {
        studentName: studentData.nama_lengkap,
        studentNIS: studentData.nis,
        registrationDate: new Date().toLocaleDateString('id-ID'),
        viewUrl: `${window.location.origin}/siswa/${studentData.id_siswa}`
      })
    );

    const results = await Promise.allSettled(promises);
    return results.map((result, index) => ({
      email: adminEmails[index],
      success: result.status === 'fulfilled' && result.value.success,
      result: result.status === 'fulfilled' ? result.value : result.reason
    }));
  }

  // Send notification when file is uploaded
  async sendFileUploadedEmail(adminEmails, studentData, fileData) {
    const promises = adminEmails.map(email => 
      this.sendEmail(email, 'fileUploaded', {
        studentName: studentData.nama_lengkap,
        fileName: fileData.nama_file,
        fileType: fileData.jenis_berkas,
        uploadDate: new Date().toLocaleDateString('id-ID'),
        viewUrl: `${window.location.origin}/siswa/${studentData.id_siswa}/berkas`
      })
    );

    const results = await Promise.allSettled(promises);
    return results.map((result, index) => ({
      email: adminEmails[index],
      success: result.status === 'fulfilled' && result.value.success,
      result: result.status === 'fulfilled' ? result.value : result.reason
    }));
  }

  // Send notification when student status changes
  async sendStatusChangedEmail(adminEmails, studentData, oldStatus, newStatus) {
    const promises = adminEmails.map(email => 
      this.sendEmail(email, 'statusChanged', {
        studentName: studentData.nama_lengkap,
        studentNIS: studentData.nis,
        oldStatus,
        newStatus,
        changeDate: new Date().toLocaleDateString('id-ID'),
        viewUrl: `${window.location.origin}/siswa/${studentData.id_siswa}`
      })
    );

    const results = await Promise.allSettled(promises);
    return results.map((result, index) => ({
      email: adminEmails[index],
      success: result.status === 'fulfilled' && result.value.success,
      result: result.status === 'fulfilled' ? result.value : result.reason
    }));
  }

  // Send system maintenance notification
  async sendMaintenanceEmail(allUserEmails, maintenanceData) {
    const promises = allUserEmails.map(email => 
      this.sendEmail(email, 'systemMaintenance', {
        maintenanceDate: maintenanceData.date,
        maintenanceTime: maintenanceData.time,
        duration: maintenanceData.duration,
        reason: maintenanceData.reason,
        contactEmail: '<EMAIL>'
      })
    );

    const results = await Promise.allSettled(promises);
    return results.map((result, index) => ({
      email: allUserEmails[index],
      success: result.status === 'fulfilled' && result.value.success,
      result: result.status === 'fulfilled' ? result.value : result.reason
    }));
  }

  // Bulk email sending with rate limiting
  async sendBulkEmails(emails, templateKey, data, options = {}) {
    const { batchSize = 10, delayBetweenBatches = 2000 } = options;
    const results = [];

    for (let i = 0; i < emails.length; i += batchSize) {
      const batch = emails.slice(i, i + batchSize);
      const batchPromises = batch.map(email => this.sendEmail(email, templateKey, data));
      
      const batchResults = await Promise.allSettled(batchPromises);
      results.push(...batchResults.map((result, index) => ({
        email: batch[index],
        success: result.status === 'fulfilled' && result.value.success,
        result: result.status === 'fulfilled' ? result.value : result.reason
      })));

      // Delay between batches to avoid overwhelming the email service
      if (i + batchSize < emails.length) {
        await new Promise(resolve => setTimeout(resolve, delayBetweenBatches));
      }
    }

    return results;
  }

  // Get email sending statistics
  getStats() {
    // In a real implementation, this would fetch from database
    return {
      totalSent: Math.floor(Math.random() * 1000) + 500,
      successRate: 0.95,
      lastSent: new Date(Date.now() - Math.random() * 86400000).toISOString(),
      templatesUsed: Object.keys(this.templates).length,
      isEnabled: this.isEnabled
    };
  }

  // Enable/disable email service
  setEnabled(enabled) {
    this.isEnabled = enabled;
    console.log(`Email service ${enabled ? 'enabled' : 'disabled'}`);
  }

  // Test email configuration
  async testConnection() {
    try {
      // Simulate connection test
      await new Promise(resolve => setTimeout(resolve, 500));
      
      const success = Math.random() > 0.2; // 80% success rate for testing
      
      if (success) {
        return {
          success: true,
          message: 'Email service connection successful',
          timestamp: new Date().toISOString()
        };
      } else {
        return {
          success: false,
          error: 'Failed to connect to email service',
          timestamp: new Date().toISOString()
        };
      }
    } catch (error) {
      return {
        success: false,
        error: error.message,
        timestamp: new Date().toISOString()
      };
    }
  }
}

// Create singleton instance
const emailService = new EmailService();

export default emailService;
