<?php
// Start session and error reporting
session_start();
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Load dependencies manually (since we're not using PSR-4 namespaces)
// Check if vendor/autoload.php exists, if not, skip it
if (file_exists(__DIR__ . '/../vendor/autoload.php')) {
    require_once __DIR__ . '/../vendor/autoload.php';
}

// Load core classes
require_once __DIR__ . '/../app/helpers/Security.php';
require_once __DIR__ . '/../app/helpers/SessionManager.php';
require_once __DIR__ . '/../app/models/Database.php';
require_once __DIR__ . '/../app/models/User.php';
require_once __DIR__ . '/../app/models/Siswa.php';
require_once __DIR__ . '/../app/models/Kelas.php';
require_once __DIR__ . '/../app/models/Berkas.php';
require_once __DIR__ . '/../app/controllers/AuthController.php';
require_once __DIR__ . '/../app/controllers/DashboardController.php';
require_once __DIR__ . '/../app/controllers/SiswaController.php';
require_once __DIR__ . '/../app/controllers/KelasController.php';
require_once __DIR__ . '/../app/controllers/UploadController.php';

// Initialize session manager
$sessionManager = new SessionManager();

// Parse URL and handle subdirectory
$uri = parse_url($_SERVER['REQUEST_URI'], PHP_URL_PATH);

// Remove base path if running in subdirectory
$basePath = '/siswa-app';
if (strpos($uri, $basePath) === 0) {
    $uri = substr($uri, strlen($basePath));
}

// Remove /public if present
if (strpos($uri, '/public') === 0) {
    $uri = substr($uri, 7);
}

$uri = explode('/', trim($uri, '/'));

// Default redirect
if (empty($uri[0]) || $uri[0] === '') {
    if (Security::isAuthenticated()) {
        header('Location: ' . $basePath . '/dashboard');
    } else {
        header('Location: ' . $basePath . '/login');
    }
    exit;
}

$controller = null;
$action = $uri[1] ?? 'index';
$id = $uri[2] ?? null;

// Route handling
switch ($uri[0]) {
    // Authentication routes (no auth required)
    case 'login':
        $controller = new AuthController();
        $action = 'login';
        break;

    case 'auth':
        $controller = new AuthController();
        $action = $uri[1] ?? 'login';
        break;

    case 'logout':
        $controller = new AuthController();
        $action = 'logout';
        break;

    case 'unauthorized':
        $controller = new AuthController();
        $action = 'unauthorized';
        break;

    // Protected routes (auth required)
    case 'dashboard':
        Security::requireAuth();
        $controller = new DashboardController();
        $action = 'index';
        break;

    case 'siswa':
        Security::requireAuth();
        $controller = new SiswaController();
        break;

    case 'kelas':
        Security::requireAuth();
        $controller = new KelasController();
        break;

    case 'upload':
        Security::requireAuth();
        Security::requireRole(['admin', 'staff']);
        $controller = new UploadController();
        break;

    case 'profile':
        Security::requireAuth();
        $controller = new AuthController();
        $action = 'profile';
        break;

    case 'users':
        Security::requireAuth();
        Security::requireRole('admin');
        $controller = new AuthController();
        $action = 'users';
        break;

    case 'register':
        Security::requireAuth();
        Security::requireRole('admin');
        $controller = new AuthController();
        $action = 'register';
        break;

    // Static assets
    case 'assets':
        // Serve static files
        $filePath = __DIR__ . $_SERVER['REQUEST_URI'];
        if (file_exists($filePath)) {
            $mimeType = mime_content_type($filePath);
            header('Content-Type: ' . $mimeType);
            readfile($filePath);
        } else {
            http_response_code(404);
        }
        exit;

    default:
        http_response_code(404);
        echo "404 Not Found";
        exit;
}

// Execute controller action
if ($controller && method_exists($controller, $action)) {
    try {
        if ($id) {
            $controller->$action($id);
        } else {
            $controller->$action();
        }
    } catch (Exception $e) {
        error_log("Controller error: " . $e->getMessage());
        http_response_code(500);
        echo "Internal Server Error";
    }
} else {
    http_response_code(404);
    echo "404 Not Found";
}
?>