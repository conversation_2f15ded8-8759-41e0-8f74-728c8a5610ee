<?php
// Error reporting - Enhanced for debugging
error_reporting(E_ALL);
ini_set('display_errors', 1);
ini_set('log_errors', 1);
ini_set('error_log', __DIR__ . '/../logs/php_errors.log');

// Load dependencies manually (since we're not using PSR-4 namespaces)
// Check if vendor/autoload.php exists, if not, skip it
if (file_exists(__DIR__ . '/../vendor/autoload.php')) {
    require_once __DIR__ . '/../vendor/autoload.php';
}

// Load core classes in correct order
require_once __DIR__ . '/../app/models/Database.php';
require_once __DIR__ . '/../app/helpers/Security.php';
require_once __DIR__ . '/../app/helpers/SimpleSessionManager.php';

// Initialize session manager first (this will configure and start session)
$sessionManager = new SimpleSessionManager();

// Load other models and controllers
require_once __DIR__ . '/../app/models/User.php';
require_once __DIR__ . '/../app/models/Siswa.php';
require_once __DIR__ . '/../app/models/Kelas.php';
require_once __DIR__ . '/../app/models/Berkas.php';
require_once __DIR__ . '/../app/controllers/AuthController.php';
require_once __DIR__ . '/../app/controllers/DashboardController.php';
require_once __DIR__ . '/../app/controllers/SiswaController.php';
require_once __DIR__ . '/../app/controllers/KelasController.php';
require_once __DIR__ . '/../app/controllers/UploadController.php';
require_once __DIR__ . '/../app/controllers/BerkasController.php';

// Parse URL and handle subdirectory
$uri = parse_url($_SERVER['REQUEST_URI'], PHP_URL_PATH);

// Remove base path if running in subdirectory
$basePath = '/siswa-app';
if (strpos($uri, $basePath) === 0) {
    $uri = substr($uri, strlen($basePath));
}

// Remove /public if present
if (strpos($uri, '/public') === 0) {
    $uri = substr($uri, 7);
}

$uri = explode('/', trim($uri, '/'));

// Debug output (remove in production)
// echo "Debug URI: " . print_r($uri, true) . "<br>";

// Default redirect
if (empty($uri[0]) || $uri[0] === '') {
    if (Security::isAuthenticated()) {
        header('Location: ' . $basePath . '/public/dashboard');
    } else {
        header('Location: ' . $basePath . '/public/login');
    }
    exit;
}

$controller = null;
$action = $uri[1] ?? 'index';
$id = $uri[2] ?? null;

// Route handling
switch ($uri[0]) {
    // Authentication routes (no auth required)
    case 'login':
        $controller = new AuthController();
        $action = 'login';
        break;

    case 'auth':
        $controller = new AuthController();
        $action = $uri[1] ?? 'login';
        // Handle POST requests for login
        if ($_SERVER['REQUEST_METHOD'] === 'POST' && $action === 'login') {
            $action = 'processLogin';
        }
        break;

    case 'logout':
        $controller = new AuthController();
        $action = 'logout';
        break;

    case 'unauthorized':
        $controller = new AuthController();
        $action = 'unauthorized';
        break;

    // Protected routes (auth required)
    case 'dashboard':
        Security::requireAuth();
        $controller = new DashboardController();
        $action = 'index';
        break;

    case 'siswa':
        Security::requireAuth();
        $controller = new SiswaController();
        break;

    case 'kelas':
        Security::requireAuth();
        $controller = new KelasController();
        break;

    case 'upload':
        Security::requireAuth();
        $controller = new UploadController();
        $action = $uri[1] ?? 'index';
        // Handle upload actions with parameters
        if ($action === 'berkas' && isset($uri[2])) {
            $id = $uri[2];
        } elseif ($action === 'delete' && isset($uri[2])) {
            $id = $uri[2];
        } elseif ($action === 'download' && isset($uri[2])) {
            $id = $uri[2];
        }
        break;

    case 'berkas':
        Security::requireAuth();
        $controller = new BerkasController();
        $action = $uri[1] ?? 'index';
        // Handle berkas actions with parameters
        if ($action === 'index' && isset($uri[2])) {
            $id = $uri[2];
        } elseif ($action === 'category' && isset($uri[2]) && isset($uri[3])) {
            $id = $uri[2]; // siswa_id
            $category = $uri[3]; // category name
        }
        break;

    case 'profile':
        Security::requireAuth();
        $controller = new AuthController();
        $action = 'profile';
        break;

    case 'users':
        Security::requireAuth();
        Security::requireRole('admin');
        $controller = new AuthController();
        $action = 'users';
        break;

    case 'register':
        Security::requireAuth();
        Security::requireRole('admin');
        $controller = new AuthController();
        $action = 'register';
        break;

    // Static assets
    case 'assets':
        // Serve static files
        $filePath = __DIR__ . $_SERVER['REQUEST_URI'];
        if (file_exists($filePath)) {
            $mimeType = mime_content_type($filePath);
            header('Content-Type: ' . $mimeType);
            readfile($filePath);
        } else {
            http_response_code(404);
        }
        exit;

    default:
        http_response_code(404);
        echo "404 Not Found";
        exit;
}

// Execute controller action
if ($controller && method_exists($controller, $action)) {
    try {
        if (isset($category)) {
            // For berkas category action
            $controller->$action($id, $category);
        } elseif ($id) {
            $controller->$action($id);
        } else {
            $controller->$action();
        }
    } catch (Exception $e) {
        error_log("Controller error: " . $e->getMessage());
        http_response_code(500);
        echo "Internal Server Error";
    }
} else {
    http_response_code(404);
    echo "404 Not Found";
}
?>