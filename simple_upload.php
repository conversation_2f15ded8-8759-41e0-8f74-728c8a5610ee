<?php
/**
 * Simple Upload Test Page
 */

require_once 'app/helpers/Security.php';
require_once 'app/helpers/SimpleSessionManager.php';
require_once 'app/models/Berkas.php';
require_once 'app/models/Siswa.php';

$sessionManager = new SimpleSessionManager();

// Check authentication
if (!Security::isAuthenticated()) {
    echo "<p style='color: red;'>❌ Not authenticated. <a href='simple_login.php'>Please login first</a></p>";
    exit;
}

$siswaId = $_GET['siswa_id'] ?? 1;
$siswaModel = new Siswa();
$berkasModel = new Berkas();

$siswa = $siswaModel->getById($siswaId);
if (!$siswa) {
    echo "<p style='color: red;'>❌ Siswa not found</p>";
    exit;
}

// Handle upload
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        $jenisberkas = $_POST['jenis_berkas'] ?? '';
        $keterangan = $_POST['keterangan'] ?? '';
        
        if (empty($jenisberkas)) {
            throw new Exception('Jenis berkas harus dipilih');
        }

        if (!isset($_FILES['file']) || $_FILES['file']['error'] !== UPLOAD_ERR_OK) {
            throw new Exception('File tidak berhasil diupload');
        }

        $file = $_FILES['file'];
        
        // Simple validation
        if ($file['size'] > 5 * 1024 * 1024) {
            throw new Exception('File terlalu besar (max 5MB)');
        }
        
        $allowedExtensions = ['pdf', 'jpg', 'jpeg', 'png', 'gif', 'doc', 'docx'];
        $extension = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));
        
        if (!in_array($extension, $allowedExtensions)) {
            throw new Exception('Tipe file tidak diizinkan');
        }

        // Generate filename
        $originalName = $file['name'];
        $timestamp = date('YmdHis');
        $random = substr(md5(uniqid()), 0, 8);
        $systemName = "siswa_{$siswaId}_{$jenisberkas}_{$timestamp}_{$random}.{$extension}";
        
        // Upload directory
        $uploadDir = __DIR__ . '/uploads/berkas/';
        if (!is_dir($uploadDir)) {
            mkdir($uploadDir, 0755, true);
        }
        
        $filePath = $uploadDir . $systemName;
        
        // Move file
        if (!move_uploaded_file($file['tmp_name'], $filePath)) {
            throw new Exception('Gagal menyimpan file');
        }

        // Get file info
        $fileHash = hash_file('sha256', $filePath);
        $finfo = finfo_open(FILEINFO_MIME_TYPE);
        $mimeType = finfo_file($finfo, $filePath);
        finfo_close($finfo);

        // Save to database
        $currentUser = $sessionManager->getCurrentUser();
        $berkasData = [
            'siswa_id' => $siswaId,
            'jenis_berkas' => $jenisberkas,
            'nama_berkas' => $originalName,
            'nama_file_asli' => $originalName,
            'nama_file_sistem' => $systemName,
            'ukuran_file' => $file['size'],
            'mime_type' => $mimeType,
            'file_path' => 'uploads/berkas/' . $systemName,
            'file_hash' => $fileHash,
            'keterangan' => $keterangan,
            'uploaded_by' => $currentUser['id'] ?? 1
        ];

        $berkasId = $berkasModel->create($berkasData);

        echo "<div style='color: green; padding: 10px; background: #d4edda; border: 1px solid #c3e6cb; border-radius: 5px; margin: 10px 0;'>";
        echo "✅ File berhasil diupload! ID: $berkasId";
        echo "</div>";

    } catch (Exception $e) {
        echo "<div style='color: red; padding: 10px; background: #f8d7da; border: 1px solid #f5c6cb; border-radius: 5px; margin: 10px 0;'>";
        echo "❌ Error: " . htmlspecialchars($e->getMessage());
        echo "</div>";
    }
}

// Get existing files
$existingFiles = $berkasModel->getBySiswaId($siswaId);
$fileCategories = $berkasModel->getFileCategories();
?>

<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Simple Upload Test</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body class="bg-light">
    <div class="container mt-4">
        <div class="row">
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header">
                        <h5>🔗 Simple Upload Test</h5>
                        <p class="mb-0">Upload untuk: <strong><?= htmlspecialchars($siswa['nama_lengkap']) ?></strong></p>
                    </div>
                    <div class="card-body">
                        <form method="POST" enctype="multipart/form-data">
                            <div class="mb-3">
                                <label class="form-label">Jenis Berkas</label>
                                <select name="jenis_berkas" class="form-select" required>
                                    <option value="">Pilih jenis berkas...</option>
                                    <?php foreach ($fileCategories as $category => $types): ?>
                                        <optgroup label="<?= $category ?>">
                                            <?php foreach ($types as $key => $label): ?>
                                                <option value="<?= $key ?>"><?= $label ?></option>
                                            <?php endforeach; ?>
                                        </optgroup>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                            
                            <div class="mb-3">
                                <label class="form-label">File</label>
                                <input type="file" name="file" class="form-control" required 
                                       accept=".pdf,.jpg,.jpeg,.png,.gif,.doc,.docx">
                                <div class="form-text">Format: PDF, JPG, PNG, DOC (Max 5MB)</div>
                            </div>
                            
                            <div class="mb-3">
                                <label class="form-label">Keterangan</label>
                                <textarea name="keterangan" class="form-control" rows="2"></textarea>
                            </div>
                            
                            <button type="submit" class="btn btn-primary">Upload File</button>
                        </form>
                    </div>
                </div>
            </div>
            
            <div class="col-md-4">
                <div class="card">
                    <div class="card-header">
                        <h6>Files yang Ada</h6>
                    </div>
                    <div class="card-body">
                        <?php if (!empty($existingFiles)): ?>
                            <?php foreach ($existingFiles as $file): ?>
                                <div class="mb-2 p-2 border rounded">
                                    <strong><?= htmlspecialchars($file['nama_berkas']) ?></strong><br>
                                    <small class="text-muted">
                                        <?= ucfirst(str_replace('_', ' ', $file['jenis_berkas'])) ?><br>
                                        <?= number_format($file['ukuran_file'] / 1024, 1) ?> KB
                                    </small>
                                </div>
                            <?php endforeach; ?>
                        <?php else: ?>
                            <p class="text-muted">Belum ada file</p>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="mt-3">
            <a href="public/siswa/detail/<?= $siswaId ?>" class="btn btn-secondary">← Kembali ke Detail</a>
            <a href="test_upload.php" class="btn btn-info">🧪 Test System</a>
        </div>
    </div>
</body>
</html>
