<div class="container-fluid">
    <!-- Page Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="h3 mb-0 text-gray-800">
                        <i class="bi bi-speedometer2 text-primary"></i>
                        Dashboard
                    </h1>
                    <p class="text-muted mb-0">Selamat datang di Sistem Informasi Akademik Siswa</p>
                </div>
                <div class="text-end">
                    <small class="text-muted">
                        <i class="bi bi-clock"></i>
                        <?= date('l, d F Y - H:i') ?> WIB
                    </small>
                </div>
            </div>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <!-- Total Siswa -->
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-primary shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                Total Siswa
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                <?= number_format($stats['total_siswa']) ?>
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="bi bi-people fa-2x text-gray-300"></i>
                        </div>
                    </div>
                    <div class="mt-2">
                        <small class="text-success">
                            <i class="bi bi-check-circle"></i>
                            <?= $stats['siswa_aktif'] ?> Aktif
                        </small>
                    </div>
                </div>
            </div>
        </div>

        <!-- Total Kelas -->
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-success shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                Total Kelas
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                <?= number_format($stats['total_kelas']) ?>
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="bi bi-building fa-2x text-gray-300"></i>
                        </div>
                    </div>
                    <div class="mt-2">
                        <small class="text-success">
                            <i class="bi bi-check-circle"></i>
                            <?= $stats['kelas_aktif'] ?> Aktif
                        </small>
                    </div>
                </div>
            </div>
        </div>

        <!-- Total Berkas -->
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-info shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                Total Berkas
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                <?= number_format($stats['total_berkas']) ?>
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="bi bi-file-earmark fa-2x text-gray-300"></i>
                        </div>
                    </div>
                    <div class="mt-2">
                        <small class="text-info">
                            <i class="bi bi-upload"></i>
                            Dokumen Digital
                        </small>
                    </div>
                </div>
            </div>
        </div>

        <!-- Siswa Lulus -->
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-warning shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                Siswa Lulus
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                <?= number_format($stats['siswa_lulus']) ?>
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="bi bi-mortarboard fa-2x text-gray-300"></i>
                        </div>
                    </div>
                    <div class="mt-2">
                        <small class="text-warning">
                            <i class="bi bi-trophy"></i>
                            Alumni
                        </small>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Charts Row -->
    <div class="row mb-4">
        <!-- Students per Class Chart -->
        <div class="col-xl-8 col-lg-7">
            <div class="card shadow mb-4">
                <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="bi bi-bar-chart"></i>
                        Distribusi Siswa per Kelas
                    </h6>
                    <div class="dropdown no-arrow">
                        <a class="dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="bi bi-three-dots-vertical text-gray-400"></i>
                        </a>
                        <div class="dropdown-menu dropdown-menu-right shadow">
                            <a class="dropdown-item" href="#" onclick="refreshChart('siswaPerKelasChart')">
                                <i class="bi bi-arrow-clockwise"></i> Refresh
                            </a>
                            <a class="dropdown-item" href="/reports">
                                <i class="bi bi-download"></i> Download Report
                            </a>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <div class="chart-area">
                        <canvas id="siswaPerKelasChart" width="400" height="200"></canvas>
                    </div>
                </div>
            </div>
        </div>

        <!-- Gender Distribution -->
        <div class="col-xl-4 col-lg-5">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="bi bi-pie-chart"></i>
                        Distribusi Jenis Kelamin
                    </h6>
                </div>
                <div class="card-body">
                    <div class="chart-pie pt-4 pb-2">
                        <canvas id="genderChart"></canvas>
                    </div>
                    <div class="mt-4 text-center small">
                        <span class="mr-2">
                            <i class="bi bi-circle-fill text-primary"></i> Laki-laki
                        </span>
                        <span class="mr-2">
                            <i class="bi bi-circle-fill text-success"></i> Perempuan
                        </span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Recent Activities and Quick Actions -->
    <div class="row">
        <!-- Recent Activities -->
        <div class="col-lg-6 mb-4">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="bi bi-activity"></i>
                        Aktivitas Terbaru
                    </h6>
                </div>
                <div class="card-body">
                    <div class="timeline">
                        <?php foreach ($recent_activities as $activity): ?>
                        <div class="timeline-item">
                            <div class="timeline-marker">
                                <i class="bi <?= $activity['icon'] ?> text-<?= $activity['color'] ?>"></i>
                            </div>
                            <div class="timeline-content">
                                <p class="mb-1"><?= htmlspecialchars($activity['message']) ?></p>
                                <small class="text-muted"><?= $activity['time'] ?></small>
                            </div>
                        </div>
                        <?php endforeach; ?>
                    </div>
                    <div class="text-center mt-3">
                        <a href="/logs" class="btn btn-sm btn-outline-primary">
                            <i class="bi bi-eye"></i> Lihat Semua Aktivitas
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="col-lg-6 mb-4">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="bi bi-lightning"></i>
                        Aksi Cepat
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-6 mb-3">
                            <a href="/siswa/create" class="btn btn-primary btn-block">
                                <i class="bi bi-person-plus"></i><br>
                                <small>Tambah Siswa</small>
                            </a>
                        </div>
                        <div class="col-6 mb-3">
                            <a href="/kelas/create" class="btn btn-success btn-block">
                                <i class="bi bi-building"></i><br>
                                <small>Tambah Kelas</small>
                            </a>
                        </div>
                        <div class="col-6 mb-3">
                            <a href="/upload" class="btn btn-info btn-block">
                                <i class="bi bi-upload"></i><br>
                                <small>Upload Berkas</small>
                            </a>
                        </div>
                        <div class="col-6 mb-3">
                            <a href="/reports" class="btn btn-warning btn-block">
                                <i class="bi bi-file-text"></i><br>
                                <small>Laporan</small>
                            </a>
                        </div>
                    </div>
                    
                    <?php if (Security::hasRole('admin')): ?>
                    <hr>
                    <div class="row">
                        <div class="col-6">
                            <a href="/users" class="btn btn-outline-secondary btn-block btn-sm">
                                <i class="bi bi-people"></i> Kelola User
                            </a>
                        </div>
                        <div class="col-6">
                            <a href="/settings" class="btn btn-outline-secondary btn-block btn-sm">
                                <i class="bi bi-gear"></i> Pengaturan
                            </a>
                        </div>
                    </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Chart.js configurations
document.addEventListener('DOMContentLoaded', function() {
    // Students per Class Chart
    const siswaPerKelasCtx = document.getElementById('siswaPerKelasChart').getContext('2d');
    const siswaPerKelasData = <?= json_encode($chart_data['siswa_per_kelas']) ?>;

    new Chart(siswaPerKelasCtx, {
        type: 'bar',
        data: {
            labels: siswaPerKelasData.map(item => item.label),
            datasets: [{
                label: 'Jumlah Siswa',
                data: siswaPerKelasData.map(item => item.count),
                backgroundColor: [
                    'rgba(54, 162, 235, 0.8)',
                    'rgba(255, 99, 132, 0.8)',
                    'rgba(255, 205, 86, 0.8)',
                    'rgba(75, 192, 192, 0.8)',
                    'rgba(153, 102, 255, 0.8)',
                    'rgba(255, 159, 64, 0.8)'
                ],
                borderColor: [
                    'rgba(54, 162, 235, 1)',
                    'rgba(255, 99, 132, 1)',
                    'rgba(255, 205, 86, 1)',
                    'rgba(75, 192, 192, 1)',
                    'rgba(153, 102, 255, 1)',
                    'rgba(255, 159, 64, 1)'
                ],
                borderWidth: 1
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: true,
                    ticks: {
                        stepSize: 1
                    }
                }
            },
            plugins: {
                legend: {
                    display: false
                },
                tooltip: {
                    callbacks: {
                        label: function(context) {
                            return context.parsed.y + ' siswa';
                        }
                    }
                }
            }
        }
    });

    // Gender Distribution Chart
    const genderCtx = document.getElementById('genderChart').getContext('2d');
    const genderData = <?= json_encode($chart_data['siswa_by_gender']) ?>;

    new Chart(genderCtx, {
        type: 'doughnut',
        data: {
            labels: ['Laki-laki', 'Perempuan'],
            datasets: [{
                data: [genderData.L, genderData.P],
                backgroundColor: [
                    'rgba(54, 162, 235, 0.8)',
                    'rgba(75, 192, 192, 0.8)'
                ],
                borderColor: [
                    'rgba(54, 162, 235, 1)',
                    'rgba(75, 192, 192, 1)'
                ],
                borderWidth: 2
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'bottom',
                    labels: {
                        padding: 20,
                        usePointStyle: true
                    }
                },
                tooltip: {
                    callbacks: {
                        label: function(context) {
                            const total = context.dataset.data.reduce((a, b) => a + b, 0);
                            const percentage = ((context.parsed / total) * 100).toFixed(1);
                            return context.label + ': ' + context.parsed + ' (' + percentage + '%)';
                        }
                    }
                }
            }
        }
    });
});

// Refresh chart function
function refreshChart(chartId) {
    // In a real application, you would fetch new data via AJAX
    location.reload();
}

// Auto-refresh dashboard every 5 minutes
setInterval(function() {
    // You can implement AJAX refresh here
    console.log('Dashboard auto-refresh triggered');
}, 300000); // 5 minutes
</script>

<style>
/* Custom Dashboard Styles */
.border-left-primary {
    border-left: 0.25rem solid #4e73df !important;
}

.border-left-success {
    border-left: 0.25rem solid #1cc88a !important;
}

.border-left-info {
    border-left: 0.25rem solid #36b9cc !important;
}

.border-left-warning {
    border-left: 0.25rem solid #f6c23e !important;
}

.text-gray-800 {
    color: #5a5c69 !important;
}

.text-gray-300 {
    color: #dddfeb !important;
}

.font-weight-bold {
    font-weight: 700 !important;
}

.text-xs {
    font-size: 0.7rem;
}

.fa-2x {
    font-size: 2em;
}

/* Timeline Styles */
.timeline {
    position: relative;
    padding-left: 30px;
}

.timeline-item {
    position: relative;
    margin-bottom: 20px;
}

.timeline-marker {
    position: absolute;
    left: -35px;
    top: 0;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background: white;
    border: 2px solid #dee2e6;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 10px;
}

.timeline::before {
    content: '';
    position: absolute;
    left: -26px;
    top: 10px;
    bottom: -10px;
    width: 2px;
    background: #dee2e6;
}

.timeline-content {
    background: #f8f9fa;
    padding: 10px 15px;
    border-radius: 8px;
    border-left: 3px solid #007bff;
}

/* Quick Actions Button Styles */
.btn-block {
    display: block;
    width: 100%;
    padding: 15px 10px;
    text-align: center;
    border-radius: 8px;
    transition: all 0.3s ease;
}

.btn-block:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

.btn-block i {
    font-size: 1.5rem;
    margin-bottom: 5px;
}

/* Card Hover Effects */
.card {
    transition: all 0.3s ease;
}

.card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

/* Chart Container */
.chart-area {
    position: relative;
    height: 300px;
}

.chart-pie {
    position: relative;
    height: 250px;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .h5 {
        font-size: 1.1rem;
    }

    .btn-block {
        padding: 10px 5px;
    }

    .btn-block i {
        font-size: 1.2rem;
    }

    .timeline {
        padding-left: 20px;
    }

    .timeline-marker {
        left: -25px;
        width: 15px;
        height: 15px;
        font-size: 8px;
    }

    .timeline::before {
        left: -18px;
    }
}
</style>
