{"common": {"loading": "Memuat...", "error": "<PERSON><PERSON><PERSON><PERSON>", "success": "<PERSON><PERSON><PERSON><PERSON>", "warning": "Peringatan", "info": "Informasi", "save": "Simpan", "cancel": "<PERSON><PERSON>", "delete": "Hapus", "edit": "Edit", "add": "Tambah", "search": "<PERSON><PERSON>", "filter": "Filter", "reset": "Reset", "refresh": "Refresh", "download": "Download", "upload": "Upload", "close": "<PERSON><PERSON><PERSON>", "back": "Kembali", "next": "Selanjutnya", "previous": "Sebelumnya", "yes": "Ya", "no": "Tidak", "confirm": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "required": "<PERSON><PERSON><PERSON>", "optional": "Opsional"}, "navigation": {"dashboard": "Dashboard", "students": "Data Siswa", "classes": "<PERSON><PERSON>", "reports": "<PERSON><PERSON><PERSON>", "settings": "<PERSON><PERSON><PERSON><PERSON>", "logout": "<PERSON><PERSON><PERSON>"}, "auth": {"login": "<PERSON><PERSON><PERSON>", "logout": "<PERSON><PERSON><PERSON>", "username": "Username", "password": "Password", "loginTitle": "Masuk ke Sistem", "loginSubtitle": "Ma<PERSON>kkan kredensial Anda untuk mengakses sistem", "loginButton": "<PERSON><PERSON><PERSON>", "loginSuccess": "Login berhasil! Selamat datang.", "loginError": "<PERSON><PERSON><PERSON> atau password salah", "sessionExpired": "<PERSON><PERSON> <PERSON>a telah be<PERSON>. <PERSON><PERSON><PERSON> login kembali.", "unauthorized": "Anda tidak memiliki izin untuk mengakses halaman ini"}, "dashboard": {"title": "Dashboard", "welcome": "Selamat datang di Sistem Informasi Akademik", "subtitle": "Kelola data siswa, ri<PERSON><PERSON> kelas, dan berkas digital dengan mudah", "lastUpdated": "<PERSON><PERSON><PERSON>", "totalStudents": "Total Siswa", "activeStudents": "Siswa Aktif", "graduatedThisYear": "<PERSON><PERSON>", "onlineNow": "Online Sekarang", "quickActions": "<PERSON><PERSON><PERSON>", "recentActivities": "Aktivitas Terbaru", "studentStatus": "Status Siswa", "genderDistribution": "Distribusi Gender", "studentsPerClass": "Distribusi Siswa per Kelas", "registrationTrend": "Trend Pendaftaran Siswa (2024)"}, "students": {"title": "<PERSON><PERSON><PERSON>", "addStudent": "Tambah Siswa", "searchPlaceholder": "<PERSON>i berda<PERSON> nama atau NIS...", "status": "Status", "class": "<PERSON><PERSON>", "allStatus": "Semua Status", "allClasses": "<PERSON><PERSON><PERSON>", "active": "Aktif", "graduated": "<PERSON><PERSON>", "transferred": "<PERSON><PERSON><PERSON>", "dropout": "Dropout", "resetFilter": "Reset Filter", "showingResults": "Menampilkan {{count}} dari {{total}} siswa", "noResults": "Tidak ada siswa yang di<PERSON>ukan", "searchFor": "untuk pencarian \"{{query}}\"", "detail": "Detail", "uploadFiles": "Upload <PERSON><PERSON><PERSON>", "noStudents": "Belum ada data siswa", "addFirstStudent": "Tambahkan siswa pertama untuk memulai", "page": "<PERSON><PERSON>", "of": "dari", "totalStudentsCount": "{{count}} total siswa"}, "studentDetail": {"title": "Detail Siswa", "personalInfo": "Informasi Pribadi", "academicInfo": "Informasi Akademik", "contactInfo": "Informasi Kontak", "documents": "Berkas", "classHistory": "Riwayat Kelas", "fullName": "<PERSON><PERSON>", "nis": "NIS", "nisn": "NISN", "gender": "<PERSON><PERSON>", "birthPlace": "Tempat Lahir", "birthDate": "<PERSON><PERSON>", "religion": "<PERSON><PERSON>a", "address": "<PERSON><PERSON><PERSON>", "phone": "No. Telepon", "email": "Email", "parentName": "<PERSON><PERSON>", "parentPhone": "No. Telepon Orang Tua", "initialClass": "<PERSON><PERSON>", "currentClass": "<PERSON>las Saat Ini", "status": "Status", "enrollmentDate": "<PERSON><PERSON>", "graduationDate": "Tanggal Lulus", "male": "<PERSON><PERSON>-laki", "female": "Perempuan"}, "fileUpload": {"title": "Upload <PERSON><PERSON><PERSON>", "dragDrop": "Drag & drop file atau", "selectFile": "<PERSON><PERSON><PERSON>", "maxSize": "Maksimal {{size}} per file", "maxFiles": "Maksimal {{count}} file", "formats": "Format", "selectedFiles": "File yang dipilih", "uploading": "Mengupload file...", "uploadSuccess": "File {{filename}} berhasil dipilih", "uploadError": "File tidak valid", "removeFile": "Hapus file", "fileTypes": {"rapor": "<PERSON><PERSON>", "ijazah": "<PERSON><PERSON><PERSON><PERSON>", "kartu_keluarga": "<PERSON><PERSON><PERSON>", "akta_lahir": "<PERSON><PERSON><PERSON>", "foto": "Foto"}}, "notifications": {"title": "Notif<PERSON><PERSON>", "markAllRead": "Tandai semua dibaca", "deleteAll": "<PERSON><PERSON>", "viewAll": "<PERSON><PERSON>", "noNotifications": "Tidak ada notifikasi", "newNotification": "Anda memiliki notifikasi baru", "deleteNotification": "<PERSON><PERSON> notif<PERSON>", "justNow": "Baru saja", "minutesAgo": "{{count}} menit yang lalu", "hoursAgo": "{{count}} jam yang lalu", "daysAgo": "{{count}} hari yang lalu"}, "errors": {"networkError": "Tidak dapat terhubung ke server. Periksa koneksi internet Anda.", "validationError": "Data yang dikirim tidak valid.", "unauthorized": "<PERSON><PERSON> <PERSON>a telah be<PERSON>. <PERSON><PERSON><PERSON> login kembali.", "forbidden": "Anda tidak memiliki izin untuk melakukan aksi ini.", "notFound": "Data yang dicari tidak ditemukan.", "conflict": "Data sudah ada atau terjadi konflik.", "serverError": "<PERSON><PERSON><PERSON><PERSON> k<PERSON><PERSON>han pada server. <PERSON><PERSON>an coba lagi nanti.", "serviceUnavailable": "Server sedang tidak tersedia. Silakan coba lagi nanti.", "unknownError": "<PERSON><PERSON><PERSON><PERSON> k<PERSON><PERSON>han yang tidak diketahui.", "tryAgain": "<PERSON><PERSON>", "backToDashboard": "Ke<PERSON>li ke Dashboard"}, "validation": {"required": "{{field}} wajib diisi", "minLength": "{{field}} minimal {{count}} karakter", "maxLength": "{{field}} maksimal {{count}} karakter", "invalidEmail": "Format email tidak valid", "invalidPhone": "Format nomor telepon tidak valid", "invalidNIS": "Format NIS tidak valid", "invalidNISN": "Format NISN tidak valid (10 digit)", "passwordTooShort": "Password minimal 6 karakter", "passwordNoLetter": "Password harus mengandung huruf", "passwordNoNumber": "Password harus mengandung angka", "fileTooLarge": "Ukuran file maksimal {{size}}", "invalidFileType": "Tipe file tidak diizinkan", "searchTooShort": "Query pencarian minimal 2 karakter", "searchTooLong": "Query pencarian maksimal 100 karakter", "invalidCharacters": "Query pencarian mengandung karakter tidak diizinkan"}, "settings": {"title": "<PERSON><PERSON><PERSON><PERSON>", "language": "Bahasa", "theme": "<PERSON><PERSON>", "notifications": "Notif<PERSON><PERSON>", "account": "<PERSON><PERSON><PERSON>", "system": "Sistem", "selectLanguage": "<PERSON><PERSON><PERSON>", "selectTheme": "<PERSON><PERSON><PERSON>", "lightTheme": "Terang", "darkTheme": "<PERSON><PERSON><PERSON>", "systemTheme": "Sistem", "emailNotifications": "Notifikasi <PERSON>", "pushNotifications": "<PERSON><PERSON><PERSON><PERSON>", "smsNotifications": "Notifikasi SMS", "notificationTypes": "<PERSON><PERSON>", "newStudent": "Siswa Baru", "fileUpload": "Upload <PERSON><PERSON><PERSON>", "transfer": "<PERSON><PERSON><PERSON>", "graduation": "<PERSON><PERSON><PERSON><PERSON>", "systemUpdates": "Update Sistem", "saveSettings": "<PERSON><PERSON><PERSON>", "settingsSaved": "<PERSON><PERSON><PERSON><PERSON> ber<PERSON><PERSON> disimpan"}}