<?php
/**
 * Test Database Structure
 */

require_once 'app/models/Database.php';
require_once 'app/models/Kelas.php';
require_once 'app/models/Siswa.php';

echo "<h2>🔍 Test Database Structure</h2>";

try {
    $db = new Database();
    
    echo "<h3>📋 Test Kelas Table</h3>";
    
    // Test kelas structure
    $kelasColumns = $db->fetchAll("DESCRIBE kelas");
    echo "<h4>Kelas Table Structure:</h4>";
    echo "<table border='1' style='border-collapse: collapse;'>";
    echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th></tr>";
    foreach ($kelasColumns as $col) {
        echo "<tr>";
        echo "<td>" . $col['Field'] . "</td>";
        echo "<td>" . $col['Type'] . "</td>";
        echo "<td>" . $col['Null'] . "</td>";
        echo "<td>" . $col['Key'] . "</td>";
        echo "<td>" . $col['Default'] . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    // Test kelas data
    $kelasModel = new Kelas();
    $kelasList = $kelasModel->getAll();
    
    echo "<h4>Kelas Data:</h4>";
    if (!empty($kelasList)) {
        echo "<table border='1' style='border-collapse: collapse;'>";
        echo "<tr><th>ID</th><th>Nama Kelas</th><th>Tingkat</th><th>Jurusan</th></tr>";
        foreach ($kelasList as $kelas) {
            echo "<tr>";
            echo "<td>" . ($kelas['id_kelas'] ?? 'N/A') . "</td>";
            echo "<td>" . ($kelas['nama_kelas'] ?? 'N/A') . "</td>";
            echo "<td>" . ($kelas['tingkat'] ?? 'N/A') . "</td>";
            echo "<td>" . ($kelas['jurusan'] ?? 'N/A') . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p style='color: red;'>❌ No kelas data found</p>";
    }
    
    echo "<h3>👥 Test Siswa Table</h3>";
    
    // Test siswa structure
    $siswaColumns = $db->fetchAll("DESCRIBE siswa");
    echo "<h4>Siswa Table Structure:</h4>";
    echo "<table border='1' style='border-collapse: collapse;'>";
    echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th></tr>";
    foreach ($siswaColumns as $col) {
        echo "<tr>";
        echo "<td>" . $col['Field'] . "</td>";
        echo "<td>" . $col['Type'] . "</td>";
        echo "<td>" . $col['Null'] . "</td>";
        echo "<td>" . $col['Key'] . "</td>";
        echo "<td>" . $col['Default'] . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    // Test siswa data
    $siswaModel = new Siswa();
    $siswaList = $siswaModel->getAll();
    
    echo "<h4>Siswa Data (First 3):</h4>";
    if (!empty($siswaList)) {
        echo "<table border='1' style='border-collapse: collapse;'>";
        echo "<tr><th>ID</th><th>NIS</th><th>Nama</th><th>Kelas ID</th><th>Nama Kelas</th></tr>";
        $count = 0;
        foreach ($siswaList as $siswa) {
            if ($count >= 3) break;
            echo "<tr>";
            echo "<td>" . ($siswa['id_siswa'] ?? 'N/A') . "</td>";
            echo "<td>" . ($siswa['nis'] ?? 'N/A') . "</td>";
            echo "<td>" . ($siswa['nama_lengkap'] ?? 'N/A') . "</td>";
            echo "<td>" . ($siswa['kelas_id'] ?? 'N/A') . "</td>";
            echo "<td>" . ($siswa['nama_kelas'] ?? 'N/A') . "</td>";
            echo "</tr>";
            $count++;
        }
        echo "</table>";
    } else {
        echo "<p style='color: red;'>❌ No siswa data found</p>";
    }
    
    // Test specific siswa
    echo "<h4>Test Siswa ID 1:</h4>";
    $siswa1 = $siswaModel->getById(1);
    if ($siswa1) {
        echo "<table border='1' style='border-collapse: collapse;'>";
        foreach ($siswa1 as $key => $value) {
            echo "<tr><td><strong>$key</strong></td><td>" . htmlspecialchars($value ?? 'NULL') . "</td></tr>";
        }
        echo "</table>";
    } else {
        echo "<p style='color: red;'>❌ Siswa ID 1 not found</p>";
    }
    
    echo "<h3>🔗 Test Foreign Key Relationship</h3>";
    
    // Test join query
    $joinTest = $db->fetchAll("
        SELECT s.id_siswa, s.nis, s.nama_lengkap, s.kelas_id, k.id as kelas_real_id, k.nama_kelas 
        FROM siswa s 
        LEFT JOIN kelas k ON s.kelas_id = k.id 
        LIMIT 3
    ");
    
    if (!empty($joinTest)) {
        echo "<table border='1' style='border-collapse: collapse;'>";
        echo "<tr><th>Siswa ID</th><th>NIS</th><th>Nama</th><th>Kelas ID (FK)</th><th>Kelas Real ID</th><th>Nama Kelas</th></tr>";
        foreach ($joinTest as $row) {
            echo "<tr>";
            echo "<td>" . $row['id_siswa'] . "</td>";
            echo "<td>" . $row['nis'] . "</td>";
            echo "<td>" . $row['nama_lengkap'] . "</td>";
            echo "<td>" . $row['kelas_id'] . "</td>";
            echo "<td>" . $row['kelas_real_id'] . "</td>";
            echo "<td>" . $row['nama_kelas'] . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
    echo "<h3>✅ Database Structure Test Complete</h3>";
    echo "<p style='color: green;'>All tests completed successfully!</p>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Error: " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "<pre>" . htmlspecialchars($e->getTraceAsString()) . "</pre>";
}

echo "<hr>";
echo "<p><a href='public/siswa/edit/1'>🔗 Test Edit Siswa</a></p>";
echo "<p><a href='public/siswa/detail/1'>👤 Student Detail</a></p>";
echo "<p><a href='public/siswa'>📋 Student List</a></p>";
?>
