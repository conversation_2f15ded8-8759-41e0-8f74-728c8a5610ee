<?php
/**
 * Create Admin User Script
 */

require_once 'app/config/db.php';

echo "<h2>Create Admin User</h2>";

try {
    $pdo = getDBConnection();
    echo "<p style='color: green;'>✅ Database connection successful!</p>";
    
    // Create users table if not exists
    $createUsersTable = "
    CREATE TABLE IF NOT EXISTS users (
        id INT PRIMARY KEY AUTO_INCREMENT,
        username VARCHAR(50) UNIQUE NOT NULL,
        email VARCHAR(100) UNIQUE NOT NULL,
        password VARCHAR(255) NOT NULL,
        role ENUM('admin', 'guru', 'staff') DEFAULT 'staff',
        nama_lengkap VARCHAR(100) NOT NULL,
        foto_profil VARCHAR(255),
        is_active BOOLEAN DEFAULT TRUE,
        last_login TIMESTAMP NULL,
        failed_login_attempts INT DEFAULT 0,
        locked_until TIMESTAMP NULL,
        password_reset_token VARCHAR(255) NULL,
        password_reset_expires TIMESTAMP NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        
        INDEX idx_username (username),
        INDEX idx_email (email),
        INDEX idx_role (role),
        INDEX idx_active (is_active)
    )";
    
    $pdo->exec($createUsersTable);
    echo "<p style='color: green;'>✅ Users table created/verified!</p>";
    
    // Check if admin user already exists
    $stmt = $pdo->prepare("SELECT * FROM users WHERE username = 'admin'");
    $stmt->execute();
    
    if ($stmt->rowCount() > 0) {
        echo "<p style='color: orange;'>⚠️ Admin user already exists!</p>";
    } else {
        // Create admin user
        $hashedPassword = password_hash('password', PASSWORD_DEFAULT);
        
        $insertAdmin = "
        INSERT INTO users (username, email, password, role, nama_lengkap, is_active) 
        VALUES ('admin', '<EMAIL>', ?, 'admin', 'Administrator', TRUE)";
        
        $stmt = $pdo->prepare($insertAdmin);
        $stmt->execute([$hashedPassword]);
        
        echo "<p style='color: green;'>✅ Admin user created successfully!</p>";
    }
    
    // Create sessions table if not exists
    $createSessionsTable = "
    CREATE TABLE IF NOT EXISTS sessions (
        id VARCHAR(128) PRIMARY KEY,
        user_id INT NOT NULL,
        ip_address VARCHAR(45) NOT NULL,
        user_agent TEXT,
        payload TEXT NOT NULL,
        last_activity TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
        INDEX idx_user_id (user_id),
        INDEX idx_last_activity (last_activity)
    )";
    
    $pdo->exec($createSessionsTable);
    echo "<p style='color: green;'>✅ Sessions table created/verified!</p>";
    
    // Create csrf_tokens table if not exists
    $createCSRFTable = "
    CREATE TABLE IF NOT EXISTS csrf_tokens (
        id INT PRIMARY KEY AUTO_INCREMENT,
        token VARCHAR(255) UNIQUE NOT NULL,
        user_id INT,
        expires_at TIMESTAMP NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
        INDEX idx_token (token),
        INDEX idx_expires (expires_at)
    )";
    
    $pdo->exec($createCSRFTable);
    echo "<p style='color: green;'>✅ CSRF tokens table created/verified!</p>";
    
    echo "<p style='color: green; font-weight: bold;'>🎉 Database setup completed successfully!</p>";
    echo "<p><strong>Login credentials:</strong></p>";
    echo "<ul>";
    echo "<li>Username: <code>admin</code></li>";
    echo "<li>Password: <code>password</code></li>";
    echo "</ul>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Setup failed: " . $e->getMessage() . "</p>";
}

echo "<hr>";
echo "<p><a href='public/' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>🚀 Go to Application</a></p>";
echo "<p><a href='test_db.php'>🔍 Test Database Connection</a></p>";
?>
