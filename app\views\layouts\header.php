<?php
require_once __DIR__ . '/../../helpers/Security.php';
require_once __DIR__ . '/../../helpers/SimpleSessionManager.php';

$sessionManager = new SimpleSessionManager();
$currentUser = $sessionManager->getCurrentUser();
$isAuthenticated = Security::isAuthenticated();
?>
<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= isset($title) ? $title . ' - ' : '' ?>Sistem Informasi Akademik Siswa</title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <!-- Custom CSS -->
    <link rel="stylesheet" href="/assets/css/style.css">

    <!-- Security Headers -->
    <meta http-equiv="X-Content-Type-Options" content="nosniff">
    <meta http-equiv="X-Frame-Options" content="DENY">
    <meta http-equiv="X-XSS-Protection" content="1; mode=block">
</head>
<body class="bg-light">

    <?php if ($isAuthenticated): ?>
    <!-- Navigation Bar -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary shadow-sm">
        <div class="container-fluid">
            <!-- Brand -->
            <a class="navbar-brand d-flex align-items-center" href="/dashboard">
                <img src="/assets/images/logo.png" alt="Logo" width="40" height="40" class="me-2">
                <span class="fw-bold">SISWA APP</span>
            </a>

            <!-- Mobile Toggle -->
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>

            <!-- Navigation Links -->
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link <?= strpos($_SERVER['REQUEST_URI'], '/dashboard') === 0 ? 'active' : '' ?>" href="/dashboard">
                            <i class="bi bi-speedometer2"></i> Dashboard
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link <?= strpos($_SERVER['REQUEST_URI'], '/siswa') === 0 ? 'active' : '' ?>" href="/siswa">
                            <i class="bi bi-people"></i> Data Siswa
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link <?= strpos($_SERVER['REQUEST_URI'], '/kelas') === 0 ? 'active' : '' ?>" href="/kelas">
                            <i class="bi bi-building"></i> Data Kelas
                        </a>
                    </li>
                    <?php if (Security::hasRole(['admin', 'staff'])): ?>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="bi bi-gear"></i> Manajemen
                        </a>
                        <ul class="dropdown-menu">
                            <?php if (Security::hasRole('admin')): ?>
                            <li><a class="dropdown-item" href="/users"><i class="bi bi-person-gear"></i> User</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <?php endif; ?>
                            <li><a class="dropdown-item" href="/reports"><i class="bi bi-file-earmark-text"></i> Laporan</a></li>
                        </ul>
                    </li>
                    <?php endif; ?>
                </ul>

                <!-- User Menu -->
                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle d-flex align-items-center" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="bi bi-person-circle me-1"></i>
                            <span><?= htmlspecialchars($currentUser['nama_lengkap']) ?></span>
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end">
                            <li><h6 class="dropdown-header">
                                <?= htmlspecialchars($currentUser['username']) ?><br>
                                <small class="text-muted"><?= ucfirst($currentUser['role']) ?></small>
                            </h6></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="/profile"><i class="bi bi-person"></i> Profil</a></li>
                            <li><a class="dropdown-item" href="/settings"><i class="bi bi-gear"></i> Pengaturan</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item text-danger" href="/logout"><i class="bi bi-box-arrow-right"></i> Logout</a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>
    <?php endif; ?>

    <!-- Main Content -->
    <main class="<?= $isAuthenticated ? 'container-fluid mt-4' : '' ?>">
            <a href="/siswa/create">Add Student</a> |
            <a href="/kelas">Classes</a> |
            <a href="/kelas/create">Add Class</a>
        </nav>
    </header>
    <main>