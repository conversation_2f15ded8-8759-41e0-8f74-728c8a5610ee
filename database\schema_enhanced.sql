-- Enhanced Database Schema untuk Sistem Informasi Akademik Siswa
-- Version: 2.0 with Authentication & Security
-- Created: 2025

-- Create Database
CREATE DATABASE IF NOT EXISTS siswa_app CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE siswa_app;

-- Table: users (untuk authentication)
CREATE TABLE users (
    id INT PRIMARY KEY AUTO_INCREMENT,
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    password VARCHAR(255) NOT NULL,
    role ENUM('admin', 'guru', 'staff') DEFAULT 'staff',
    nama_lengkap VARCHAR(100) NOT NULL,
    foto_profil VARCHAR(255),
    is_active BOOLEAN DEFAULT TRUE,
    last_login TIMESTAMP NULL,
    failed_login_attempts INT DEFAULT 0,
    locked_until TIMES<PERSON>MP NULL,
    password_reset_token VARCHAR(255) NULL,
    password_reset_expires TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_username (username),
    INDEX idx_email (email),
    INDEX idx_role (role),
    INDEX idx_active (is_active)
);

-- Table: sessions (untuk session management)
CREATE TABLE sessions (
    id VARCHAR(128) PRIMARY KEY,
    user_id INT NOT NULL,
    ip_address VARCHAR(45) NOT NULL,
    user_agent TEXT,
    payload TEXT NOT NULL,
    last_activity TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_user_id (user_id),
    INDEX idx_last_activity (last_activity)
);

-- Table: csrf_tokens (untuk CSRF protection)
CREATE TABLE csrf_tokens (
    id INT PRIMARY KEY AUTO_INCREMENT,
    token VARCHAR(255) UNIQUE NOT NULL,
    user_id INT,
    expires_at TIMESTAMP NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_token (token),
    INDEX idx_expires (expires_at)
);

-- Enhanced Table: siswa (dengan security fields)
CREATE TABLE siswa (
    id_siswa INT PRIMARY KEY AUTO_INCREMENT,
    nis VARCHAR(20) UNIQUE NOT NULL,
    nisn VARCHAR(20) UNIQUE,
    nama_lengkap VARCHAR(100) NOT NULL,
    jenis_kelamin ENUM('L', 'P') NOT NULL,
    tempat_lahir VARCHAR(50),
    tanggal_lahir DATE,
    alamat TEXT,
    no_telepon VARCHAR(20),
    email VARCHAR(100),
    nama_ayah VARCHAR(100),
    nama_ibu VARCHAR(100),
    pekerjaan_ayah VARCHAR(50),
    pekerjaan_ibu VARCHAR(50),
    kelas_id INT,
    tahun_masuk YEAR,
    status_siswa ENUM('aktif', 'lulus', 'mutasi', 'dropout') DEFAULT 'aktif',
    foto VARCHAR(255),
    created_by INT,
    updated_by INT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (kelas_id) REFERENCES kelas(id),
    FOREIGN KEY (created_by) REFERENCES users(id),
    FOREIGN KEY (updated_by) REFERENCES users(id),
    INDEX idx_nis (nis),
    INDEX idx_nama (nama_lengkap),
    INDEX idx_status (status_siswa),
    INDEX idx_kelas (kelas_id)
);

-- Enhanced Table: kelas (dengan security fields)
CREATE TABLE kelas (
    id INT PRIMARY KEY AUTO_INCREMENT,
    nama_kelas VARCHAR(20) NOT NULL,
    tingkat INT NOT NULL,
    jurusan VARCHAR(50),
    tahun_pelajaran VARCHAR(9) NOT NULL,
    wali_kelas VARCHAR(100),
    kapasitas INT DEFAULT 30,
    is_active BOOLEAN DEFAULT TRUE,
    created_by INT,
    updated_by INT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (created_by) REFERENCES users(id),
    FOREIGN KEY (updated_by) REFERENCES users(id),
    INDEX idx_tingkat (tingkat),
    INDEX idx_tahun (tahun_pelajaran),
    INDEX idx_active (is_active)
);

-- Enhanced Table: berkas (dengan security fields)
CREATE TABLE berkas (
    id INT PRIMARY KEY AUTO_INCREMENT,
    siswa_id INT NOT NULL,
    jenis_berkas ENUM('kartu_keluarga', 'akta_lahir', 'rapor', 'ijazah_sebelumnya', 'foto', 'surat_keterangan_sehat', 'lainnya') NOT NULL,
    nama_berkas VARCHAR(255) NOT NULL,
    nama_file_asli VARCHAR(255) NOT NULL,
    nama_file_sistem VARCHAR(255) NOT NULL,
    ukuran_file INT NOT NULL,
    mime_type VARCHAR(100) NOT NULL,
    file_path VARCHAR(500) NOT NULL,
    file_hash VARCHAR(64) NOT NULL, -- SHA-256 hash untuk integrity check
    keterangan TEXT,
    uploaded_by INT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (siswa_id) REFERENCES siswa(id_siswa) ON DELETE CASCADE,
    FOREIGN KEY (uploaded_by) REFERENCES users(id),
    INDEX idx_siswa (siswa_id),
    INDEX idx_jenis (jenis_berkas),
    INDEX idx_hash (file_hash)
);

-- Table: audit_logs (untuk security audit)
CREATE TABLE audit_logs (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT,
    action VARCHAR(50) NOT NULL,
    table_name VARCHAR(50) NOT NULL,
    record_id INT,
    old_values JSON,
    new_values JSON,
    ip_address VARCHAR(45),
    user_agent TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (user_id) REFERENCES users(id),
    INDEX idx_user (user_id),
    INDEX idx_action (action),
    INDEX idx_table (table_name),
    INDEX idx_created (created_at)
);

-- Insert default admin user
INSERT INTO users (username, email, password, role, nama_lengkap, is_active) VALUES 
('admin', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'admin', 'Administrator', TRUE);
-- Password: password (hashed with bcrypt)

-- Insert sample classes
INSERT INTO kelas (nama_kelas, tingkat, jurusan, tahun_pelajaran, wali_kelas, created_by) VALUES 
('X-IPA-1', 10, 'IPA', '2024/2025', 'Budi Santoso', 1),
('X-IPS-1', 10, 'IPS', '2024/2025', 'Siti Aminah', 1),
('XI-IPA-1', 11, 'IPA', '2024/2025', 'Ahmad Rahman', 1);
