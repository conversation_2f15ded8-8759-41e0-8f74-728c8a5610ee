<?php
/**
 * Debug Routing Script
 * Shows how URLs are parsed
 */

echo "<h2>Debug Routing</h2>";

// Simulate different URLs
$testUrls = [
    '/siswa-app/',
    '/siswa-app/public/',
    '/siswa-app/public/login',
    '/siswa-app/public/auth/login',
    '/siswa-app/public/dashboard',
    '/siswa-app/login',
    '/siswa-app/auth/login'
];

foreach ($testUrls as $testUri) {
    echo "<h3>Testing URL: $testUri</h3>";
    
    // Parse URL and handle subdirectory
    $uri = $testUri;
    
    // Remove base path if running in subdirectory
    $basePath = '/siswa-app';
    if (strpos($uri, $basePath) === 0) {
        $uri = substr($uri, strlen($basePath));
    }
    
    // Remove /public if present
    if (strpos($uri, '/public') === 0) {
        $uri = substr($uri, 7);
    }
    
    $uriArray = explode('/', trim($uri, '/'));
    
    echo "<p>After processing: <code>" . print_r($uriArray, true) . "</code></p>";
    echo "<p>Controller would be: <strong>" . ($uriArray[0] ?: 'default') . "</strong></p>";
    echo "<p>Action would be: <strong>" . ($uriArray[1] ?? 'index') . "</strong></p>";
    echo "<hr>";
}

echo "<h3>Current Request Info</h3>";
echo "<p>REQUEST_URI: <code>" . ($_SERVER['REQUEST_URI'] ?? 'Not set') . "</code></p>";
echo "<p>SCRIPT_NAME: <code>" . ($_SERVER['SCRIPT_NAME'] ?? 'Not set') . "</code></p>";
echo "<p>PATH_INFO: <code>" . ($_SERVER['PATH_INFO'] ?? 'Not set') . "</code></p>";

echo "<hr>";
echo "<p><a href='public/'>Go to Application</a></p>";
?>
