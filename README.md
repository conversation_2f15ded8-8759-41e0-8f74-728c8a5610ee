Siswa App
A simple PHP-based student management system built with the MVC pattern.
Features

Manage students (create, read, update, delete).
Manage classes.
Upload and view files for students.

Requirements

PHP 7.4+
MySQL
Apache with mod_rewrite
Composer

Setup

Clone or download the project to your web server (e.g., /var/www/html/siswa-app).
Create a MySQL database named siswa_app and import the schema:CREATE DATABASE siswa_app;
USE siswa_app;
-- (See SQL schema in documentation or code)


Update app/config/db.php with your database credentials.
Run composer install and composer dump-autoload in the project root.
Ensure the public/uploads/ directory is writable (for file uploads).
Add a logo.png file to public/assets/images/.
Access the app at http://localhost/siswa-app.

Directory Structure

public/: Web root (index.php, assets).
app/: Core application (controllers, models, views, config, helpers).
.htaccess: URL rewriting.
composer.json: Dependency management.

Usage

Navigate to /siswa to view students.
Use /siswa/create or /siswa/edit/{id} to manage student data.
Go to /kelas to view classes and /kelas/create to add a class.
Upload files for a student at /upload/berkas/{siswa_id}.

Notes

Ensure file upload directory (public/uploads/) exists and is writable.
Add validation and authentication for production use.

