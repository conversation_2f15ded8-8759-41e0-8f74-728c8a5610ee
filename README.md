# 🎓 Sistem Informasi Akademik Siswa

Aplikasi manajemen data siswa dengan pola MVC menggunakan PHP, MySQL, dan <PERSON>trap untuk interface yang responsive dan aman.

## ✨ Fitur Utama

- 🔐 **Authentication System** - Login/logout dengan role-based access control
- 📊 **Dashboard Interaktif** - Overview sistem dengan charts dan statistik real-time
- 👥 **Manajemen Data Siswa** - CRUD data siswa lengkap dengan validasi
- 📚 **Manajemen Kelas** - Pengelolaan data kelas dan distribusi siswa
- 📁 **Upload Berkas Digital** - Sistem upload dokumen dengan keamanan tinggi
- 🔒 **Keamanan Tingkat Tinggi** - CSRF protection, input validation, secure sessions
- 📱 **Responsive Design** - Tampilan optimal di semua perangkat
- 🎯 **Role-Based Access** - Admin, Guru, dan Staff dengan hak akses berbeda
- 📈 **Visualisasi Data** - Charts dan grafik untuk analisis data
- 🔍 **Audit Trail** - Logging aktivitas sistem untuk keamanan

## 🛠️ Teknologi yang Digunakan

### Backend
- **PHP 7.4+** - Server-side scripting dengan OOP
- **MySQL** - Database management system
- **PDO** - Database abstraction layer dengan prepared statements
- **Composer** - Dependency management

### Frontend
- **Bootstrap 5** - CSS framework untuk responsive design
- **Chart.js** - Library untuk visualisasi data interaktif
- **Bootstrap Icons** - Icon set yang lengkap
- **jQuery** - JavaScript library untuk interaktivitas

### Security Features
- **bcrypt Password Hashing** - Enkripsi password yang aman
- **CSRF Protection** - Token-based CSRF prevention
- **Input Validation & Sanitization** - Server-side validation
- **Secure File Upload** - File type validation dan hash checking
- **Session Management** - Custom session handler dengan database storage
- **Rate Limiting** - Protection terhadap brute force attacks
- **SQL Injection Prevention** - Prepared statements dan input sanitization

## 🚀 Instalasi dan Setup

### Prerequisites
- PHP 7.4 atau lebih baru
- MySQL 5.7 atau lebih baru
- Apache dengan mod_rewrite
- Composer

### Langkah Instalasi

1. **Clone atau download project**
   ```bash
   git clone <repository-url>
   cd siswa-app
   ```

2. **Install dependencies**
   ```bash
   composer install
   composer dump-autoload
   ```

3. **Setup Database**
   ```sql
   CREATE DATABASE siswa_app CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
   ```

   Import enhanced schema:
   ```bash
   mysql -u root -p siswa_app < database/schema_enhanced.sql
   ```

4. **Konfigurasi Database**
   Update `app/config/db.php` dengan kredensial database Anda:
   ```php
   define('DB_HOST', 'localhost');
   define('DB_USER', 'your_username');
   define('DB_PASS', 'your_password');
   define('DB_NAME', 'siswa_app');
   ```

5. **Setup Permissions**
   ```bash
   chmod 755 public/uploads/
   chmod 755 logs/
   mkdir -p public/assets/images/
   ```

6. **Add Logo**
   Tambahkan file `logo.png` ke `public/assets/images/`

7. **Access Application**
   Buka browser dan akses: `http://localhost/siswa-app`

## 📁 Struktur Direktori

```
siswa-app/
├── app/
│   ├── controllers/         # Controllers (MVC Pattern)
│   │   ├── AuthController.php
│   │   ├── DashboardController.php
│   │   ├── SiswaController.php
│   │   ├── KelasController.php
│   │   └── UploadController.php
│   ├── models/             # Models (Database Layer)
│   │   ├── User.php
│   │   ├── Siswa.php
│   │   ├── Kelas.php
│   │   ├── Berkas.php
│   │   └── Database.php
│   ├── views/              # Views (Presentation Layer)
│   │   ├── layouts/
│   │   ├── auth/
│   │   ├── dashboard/
│   │   ├── siswa/
│   │   └── kelas/
│   ├── helpers/            # Helper Classes
│   │   ├── Security.php
│   │   └── SessionManager.php
│   └── config/             # Configuration
│       └── db.php
├── public/                 # Web Root
│   ├── index.php          # Entry Point
│   ├── assets/            # Static Assets
│   │   ├── css/
│   │   ├── js/
│   │   └── images/
│   └── uploads/           # File Uploads
├── database/              # Database Files
│   └── schema_enhanced.sql
├── logs/                  # Application Logs
├── vendor/                # Composer Dependencies
├── .htaccess             # URL Rewriting
└── composer.json         # Dependencies
```

## 🔐 Default Login Credentials

**Administrator:**
- Username: `admin`
- Password: `password`

⚠️ **PENTING:** Segera ubah password default setelah login pertama!

## 📖 Penggunaan Aplikasi

### 🏠 Dashboard
- Akses: `/dashboard`
- Fitur: Statistik sistem, charts, aktivitas terbaru, quick actions

### 👥 Manajemen Siswa
- Daftar siswa: `/siswa`
- Tambah siswa: `/siswa/create`
- Edit siswa: `/siswa/edit/{id}`
- Detail siswa: `/siswa/detail/{id}`

### 🏫 Manajemen Kelas
- Daftar kelas: `/kelas`
- Tambah kelas: `/kelas/create`
- Edit kelas: `/kelas/edit/{id}`

### 📁 Upload Berkas
- Upload berkas: `/upload/berkas/{siswa_id}`
- Jenis berkas: Kartu Keluarga, Akta Lahir, Rapor, Ijazah, Foto, dll.

### 👤 Manajemen User (Admin Only)
- Daftar user: `/users`
- Tambah user: `/register`
- Profil: `/profile`

## 🔒 Fitur Keamanan

### Authentication & Authorization
- ✅ Login/logout system
- ✅ Role-based access control (Admin, Guru, Staff)
- ✅ Session management dengan database storage
- ✅ Password hashing dengan bcrypt
- ✅ Account lockout setelah failed attempts

### Input Security
- ✅ CSRF protection pada semua form
- ✅ Input validation dan sanitization
- ✅ SQL injection prevention
- ✅ XSS protection

### File Upload Security
- ✅ File type validation
- ✅ File size limitation
- ✅ Malicious content detection
- ✅ Secure filename generation
- ✅ File integrity checking (SHA-256)

### Session Security
- ✅ Secure session configuration
- ✅ Session regeneration
- ✅ Auto-logout on idle
- ✅ IP address validation

## 🎨 UI/UX Features

### Responsive Design
- ✅ Mobile-first approach
- ✅ Bootstrap 5 framework
- ✅ Adaptive navigation
- ✅ Touch-friendly interface

### User Experience
- ✅ Real-time form validation
- ✅ Loading states dan feedback
- ✅ Toast notifications
- ✅ Intuitive navigation
- ✅ Keyboard shortcuts

### Data Visualization
- ✅ Interactive charts (Chart.js)
- ✅ Statistical dashboards
- ✅ Progress indicators
- ✅ Data export capabilities

