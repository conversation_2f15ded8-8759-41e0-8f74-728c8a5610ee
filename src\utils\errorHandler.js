// Error handling utilities

/**
 * Parse API error response
 */
export const parseApiError = (error) => {
  // Network error
  if (!error.response) {
    return {
      type: 'network',
      message: 'Tidak dapat terhubung ke server. Periksa koneksi internet Anda.',
      code: 'NETWORK_ERROR'
    };
  }

  // HTTP error responses
  const { status, data } = error.response;

  switch (status) {
    case 400:
      return {
        type: 'validation',
        message: data?.message || 'Data yang dikirim tidak valid.',
        code: 'VALIDATION_ERROR',
        details: data?.errors
      };

    case 401:
      return {
        type: 'auth',
        message: data?.message || 'Sesi Anda telah berakhir. Silakan login kembali.',
        code: 'UNAUTHORIZED'
      };

    case 403:
      return {
        type: 'permission',
        message: data?.message || 'Anda tidak memiliki izin untuk melakukan aksi ini.',
        code: 'FORBIDDEN'
      };

    case 404:
      return {
        type: 'notfound',
        message: data?.message || 'Data yang dicari tidak ditemukan.',
        code: 'NOT_FOUND'
      };

    case 409:
      return {
        type: 'conflict',
        message: data?.message || 'Data sudah ada atau terjadi konflik.',
        code: 'CONFLICT'
      };

    case 422:
      return {
        type: 'validation',
        message: data?.message || 'Data tidak dapat diproses.',
        code: 'UNPROCESSABLE_ENTITY',
        details: data?.errors
      };

    case 429:
      return {
        type: 'ratelimit',
        message: 'Terlalu banyak permintaan. Silakan coba lagi nanti.',
        code: 'RATE_LIMIT'
      };

    case 500:
      return {
        type: 'server',
        message: 'Terjadi kesalahan pada server. Silakan coba lagi nanti.',
        code: 'INTERNAL_SERVER_ERROR'
      };

    case 502:
    case 503:
    case 504:
      return {
        type: 'server',
        message: 'Server sedang tidak tersedia. Silakan coba lagi nanti.',
        code: 'SERVICE_UNAVAILABLE'
      };

    default:
      return {
        type: 'unknown',
        message: data?.message || 'Terjadi kesalahan yang tidak diketahui.',
        code: 'UNKNOWN_ERROR'
      };
  }
};

/**
 * Handle API errors with toast notifications
 */
export const handleApiError = (error, showToast, customMessages = {}) => {
  const parsedError = parseApiError(error);
  
  // Use custom message if provided
  const message = customMessages[parsedError.type] || parsedError.message;
  
  // Show appropriate toast based on error type
  switch (parsedError.type) {
    case 'network':
      showToast(message, 'error', 8000);
      break;
    case 'auth':
      showToast(message, 'warning', 6000);
      // Redirect to login if needed
      if (parsedError.code === 'UNAUTHORIZED') {
        setTimeout(() => {
          window.location.href = '/login';
        }, 2000);
      }
      break;
    case 'validation':
      showToast(message, 'warning', 5000);
      break;
    case 'permission':
      showToast(message, 'error', 6000);
      break;
    case 'server':
      showToast(message, 'error', 8000);
      break;
    default:
      showToast(message, 'error', 5000);
  }

  return parsedError;
};

/**
 * Retry mechanism for failed requests
 */
export const retryRequest = async (requestFn, maxRetries = 3, delay = 1000) => {
  let lastError;

  for (let i = 0; i < maxRetries; i++) {
    try {
      return await requestFn();
    } catch (error) {
      lastError = error;
      
      // Don't retry for certain error types
      const parsedError = parseApiError(error);
      if (['auth', 'permission', 'validation'].includes(parsedError.type)) {
        throw error;
      }

      // Wait before retrying
      if (i < maxRetries - 1) {
        await new Promise(resolve => setTimeout(resolve, delay * (i + 1)));
      }
    }
  }

  throw lastError;
};

/**
 * Check if error is retryable
 */
export const isRetryableError = (error) => {
  const parsedError = parseApiError(error);
  return ['network', 'server'].includes(parsedError.type);
};

/**
 * Format validation errors for display
 */
export const formatValidationErrors = (errors) => {
  if (!errors || typeof errors !== 'object') {
    return {};
  }

  const formatted = {};
  
  for (const [field, messages] of Object.entries(errors)) {
    if (Array.isArray(messages)) {
      formatted[field] = messages[0]; // Take first error message
    } else if (typeof messages === 'string') {
      formatted[field] = messages;
    }
  }

  return formatted;
};

/**
 * Log errors for debugging
 */
export const logError = (error, context = '') => {
  if (process.env.NODE_ENV === 'development') {
    console.group(`🚨 Error ${context ? `in ${context}` : ''}`);
    console.error('Original error:', error);
    console.error('Parsed error:', parseApiError(error));
    console.groupEnd();
  }

  // In production, you might want to send errors to a logging service
  // Example: Sentry, LogRocket, etc.
};

/**
 * Create error boundary fallback component
 */
export const createErrorFallback = (message = 'Terjadi kesalahan yang tidak terduga') => {
  return ({ error, resetErrorBoundary }) => (
    <div className="min-h-screen bg-gray-50 flex flex-col justify-center py-12 sm:px-6 lg:px-8">
      <div className="sm:mx-auto sm:w-full sm:max-w-md">
        <div className="bg-white py-8 px-4 shadow sm:rounded-lg sm:px-10">
          <div className="text-center">
            <div className="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-red-100">
              <svg className="h-6 w-6 text-red-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
              </svg>
            </div>
            <h3 className="mt-4 text-lg font-medium text-gray-900">
              Oops! Terjadi Kesalahan
            </h3>
            <p className="mt-2 text-sm text-gray-500">
              {message}
            </p>
            <div className="mt-6 space-y-3">
              <button
                onClick={resetErrorBoundary}
                className="w-full inline-flex justify-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
              >
                Coba Lagi
              </button>
              <button
                onClick={() => window.location.href = '/'}
                className="w-full inline-flex justify-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
              >
                Kembali ke Dashboard
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};
