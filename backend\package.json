{"name": "siswa-app-backend", "version": "1.0.0", "description": "Backend API untuk Sistem Informasi Akademik Siswa", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "jest", "migrate": "node scripts/migrate.js", "seed": "node scripts/seed.js"}, "keywords": ["nodejs", "express", "mysql", "api", "siswa", "akademik"], "author": "Sistem Informasi Siswa", "license": "MIT", "dependencies": {"bcryptjs": "^2.4.3", "chart.js": "^4.4.9", "compression": "^1.7.4", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "express-rate-limit": "^7.1.5", "helmet": "^7.1.0", "joi": "^17.11.0", "jsonwebtoken": "^9.0.2", "moment": "^2.29.4", "morgan": "^1.10.0", "multer": "^1.4.5-lts.1", "mysql2": "^3.6.5", "react-chartjs-2": "^5.3.0", "uuid": "^9.0.1"}, "devDependencies": {"jest": "^29.7.0", "nodemon": "^3.0.2", "supertest": "^6.3.3"}, "engines": {"node": ">=16.0.0"}}