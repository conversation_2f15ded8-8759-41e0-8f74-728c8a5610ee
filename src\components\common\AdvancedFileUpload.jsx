import React, { useState, useRef, useCallback } from 'react';
import { useToast } from '../../contexts/ToastContext';
import { validateFile } from '../../utils/validation';

const AdvancedFileUpload = ({ 
  onFileSelect, 
  multiple = false, 
  accept = "image/*,.pdf,.doc,.docx",
  maxSize = 5 * 1024 * 1024, // 5MB
  maxFiles = 5,
  className = ""
}) => {
  const [dragActive, setDragActive] = useState(false);
  const [files, setFiles] = useState([]);
  const [uploading, setUploading] = useState(false);
  const inputRef = useRef(null);
  const { showError, showSuccess } = useToast();

  const handleFiles = useCallback((fileList) => {
    const newFiles = Array.from(fileList);
    const validFiles = [];
    const errors = [];

    // Check file count limit
    if (multiple && files.length + newFiles.length > maxFiles) {
      showError(`Maksimal ${maxFiles} file dapat diupload`);
      return;
    }

    // Validate each file
    newFiles.forEach((file) => {
      const validation = validateFile(file, { 
        maxSize, 
        allowedTypes: accept.split(',').map(type => {
          if (type.startsWith('.')) {
            // Convert extension to MIME type
            const ext = type.toLowerCase();
            const mimeTypes = {
              '.pdf': 'application/pdf',
              '.doc': 'application/msword',
              '.docx': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
              '.jpg': 'image/jpeg',
              '.jpeg': 'image/jpeg',
              '.png': 'image/png',
              '.gif': 'image/gif'
            };
            return mimeTypes[ext] || type;
          }
          return type.trim();
        })
      });

      if (validation.isValid) {
        validFiles.push(file);
      } else {
        errors.push(`${file.name}: ${validation.errors.join(', ')}`);
      }
    });

    // Show errors if any
    if (errors.length > 0) {
      showError(`File tidak valid:\n${errors.join('\n')}`);
    }

    // Add valid files
    if (validFiles.length > 0) {
      const updatedFiles = multiple ? [...files, ...validFiles] : validFiles;
      setFiles(updatedFiles);
      onFileSelect && onFileSelect(multiple ? updatedFiles : validFiles[0]);
      
      if (validFiles.length === 1) {
        showSuccess(`File ${validFiles[0].name} berhasil dipilih`);
      } else {
        showSuccess(`${validFiles.length} file berhasil dipilih`);
      }
    }
  }, [files, multiple, maxFiles, maxSize, accept, onFileSelect, showError, showSuccess]);

  const handleDrag = useCallback((e) => {
    e.preventDefault();
    e.stopPropagation();
    if (e.type === "dragenter" || e.type === "dragover") {
      setDragActive(true);
    } else if (e.type === "dragleave") {
      setDragActive(false);
    }
  }, []);

  const handleDrop = useCallback((e) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);
    
    if (e.dataTransfer.files && e.dataTransfer.files[0]) {
      handleFiles(e.dataTransfer.files);
    }
  }, [handleFiles]);

  const handleChange = useCallback((e) => {
    e.preventDefault();
    if (e.target.files && e.target.files[0]) {
      handleFiles(e.target.files);
    }
  }, [handleFiles]);

  const removeFile = useCallback((index) => {
    const updatedFiles = files.filter((_, i) => i !== index);
    setFiles(updatedFiles);
    onFileSelect && onFileSelect(multiple ? updatedFiles : null);
  }, [files, multiple, onFileSelect]);

  const openFileSelector = () => {
    inputRef.current?.click();
  };

  const formatFileSize = (bytes) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const getFileIcon = (file) => {
    const type = file.type.toLowerCase();
    if (type.includes('image')) return '🖼️';
    if (type.includes('pdf')) return '📄';
    if (type.includes('word') || type.includes('document')) return '📝';
    if (type.includes('excel') || type.includes('spreadsheet')) return '📊';
    return '📁';
  };

  return (
    <div className={`w-full ${className}`}>
      {/* Drop Zone */}
      <div
        className={`relative border-2 border-dashed rounded-lg p-6 text-center transition-colors ${
          dragActive
            ? 'border-blue-400 bg-blue-50'
            : 'border-gray-300 hover:border-gray-400'
        }`}
        onDragEnter={handleDrag}
        onDragLeave={handleDrag}
        onDragOver={handleDrag}
        onDrop={handleDrop}
      >
        <input
          ref={inputRef}
          type="file"
          multiple={multiple}
          accept={accept}
          onChange={handleChange}
          className="hidden"
        />

        <div className="space-y-4">
          <div className="text-4xl">📁</div>
          <div>
            <p className="text-lg font-medium text-gray-700">
              {dragActive ? 'Lepaskan file di sini' : 'Drag & drop file atau'}
            </p>
            <button
              type="button"
              onClick={openFileSelector}
              className="mt-2 inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            >
              Pilih File
            </button>
          </div>
          <div className="text-sm text-gray-500">
            <p>Maksimal {formatFileSize(maxSize)} per file</p>
            {multiple && <p>Maksimal {maxFiles} file</p>}
            <p>Format: {accept.replace(/,/g, ', ')}</p>
          </div>
        </div>
      </div>

      {/* File List */}
      {files.length > 0 && (
        <div className="mt-4 space-y-2">
          <h4 className="text-sm font-medium text-gray-700">File yang dipilih:</h4>
          {files.map((file, index) => (
            <div
              key={`${file.name}-${index}`}
              className="flex items-center justify-between p-3 bg-gray-50 rounded-lg border"
            >
              <div className="flex items-center space-x-3">
                <span className="text-xl">{getFileIcon(file)}</span>
                <div>
                  <p className="text-sm font-medium text-gray-900 truncate max-w-xs">
                    {file.name}
                  </p>
                  <p className="text-xs text-gray-500">
                    {formatFileSize(file.size)}
                  </p>
                </div>
              </div>
              <button
                onClick={() => removeFile(index)}
                className="text-red-500 hover:text-red-700 p-1"
                title="Hapus file"
              >
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>
          ))}
        </div>
      )}

      {/* Upload Progress */}
      {uploading && (
        <div className="mt-4">
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
            <div className="flex items-center">
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600 mr-3"></div>
              <span className="text-sm text-blue-800">Mengupload file...</span>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default AdvancedFileUpload;
