import React, { createContext, useContext, useState, useEffect, useCallback } from 'react';
import { useAuth } from './AuthContext';
import { useToast } from './ToastContext';

const NotificationContext = createContext();

export const useNotifications = () => {
  const context = useContext(NotificationContext);
  if (!context) {
    throw new Error('useNotifications must be used within a NotificationProvider');
  }
  return context;
};

// Mock notifications data
const mockNotifications = [
  {
    id: 1,
    title: 'Siswa Baru Terdaftar',
    message: '<PERSON> telah terdaftar sebagai siswa baru di kelas 10 IPA 1',
    type: 'info',
    read: false,
    timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000), // 2 hours ago
    action: {
      label: 'Lihat Detail',
      url: '/siswa/1'
    }
  },
  {
    id: 2,
    title: 'Be<PERSON>s Diupload',
    message: '<PERSON><PERSON>dah <PERSON> mengupload berkas ijazah SMP',
    type: 'success',
    read: false,
    timestamp: new Date(Date.now() - 4 * 60 * 60 * 1000), // 4 hours ago
    action: {
      label: 'Verifikasi',
      url: '/siswa/2/berkas'
    }
  },
  {
    id: 3,
    title: 'Pengajuan Mutasi',
    message: 'Muhammad Fajar Sidiq mengajukan mutasi ke SMA Negeri 2',
    type: 'warning',
    read: true,
    timestamp: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000), // 1 day ago
    action: {
      label: 'Proses',
      url: '/siswa/3'
    }
  },
  {
    id: 4,
    title: 'Backup Selesai',
    message: 'Backup database harian telah selesai dilakukan',
    type: 'success',
    read: true,
    timestamp: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000), // 2 days ago
  },
  {
    id: 5,
    title: 'Sistem Maintenance',
    message: 'Sistem akan maintenance pada Minggu, 15 Desember 2024 pukul 02:00 WIB',
    type: 'warning',
    read: false,
    timestamp: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000), // 3 days ago
  }
];

export const NotificationProvider = ({ children }) => {
  const [notifications, setNotifications] = useState(mockNotifications);
  const [unreadCount, setUnreadCount] = useState(0);
  const [isOpen, setIsOpen] = useState(false);
  const { user } = useAuth();
  const { showInfo } = useToast();

  // Calculate unread count
  useEffect(() => {
    const count = notifications.filter(n => !n.read).length;
    setUnreadCount(count);
  }, [notifications]);

  // Simulate real-time notifications
  useEffect(() => {
    if (!user) return;

    const interval = setInterval(() => {
      // Randomly add new notification (10% chance every 30 seconds)
      if (Math.random() < 0.1) {
        const newNotification = {
          id: Date.now(),
          title: 'Aktivitas Baru',
          message: 'Ada aktivitas baru di sistem',
          type: 'info',
          read: false,
          timestamp: new Date()
        };

        setNotifications(prev => [newNotification, ...prev]);
        showInfo('Anda memiliki notifikasi baru');
      }
    }, 30000); // Check every 30 seconds

    return () => clearInterval(interval);
  }, [user, showInfo]);

  const markAsRead = useCallback((notificationId) => {
    setNotifications(prev =>
      prev.map(notification =>
        notification.id === notificationId
          ? { ...notification, read: true }
          : notification
      )
    );
  }, []);

  const markAllAsRead = useCallback(() => {
    setNotifications(prev =>
      prev.map(notification => ({ ...notification, read: true }))
    );
  }, []);

  const deleteNotification = useCallback((notificationId) => {
    setNotifications(prev =>
      prev.filter(notification => notification.id !== notificationId)
    );
  }, []);

  const clearAll = useCallback(() => {
    setNotifications([]);
  }, []);

  const addNotification = useCallback((notification) => {
    const newNotification = {
      id: Date.now(),
      read: false,
      timestamp: new Date(),
      ...notification
    };
    setNotifications(prev => [newNotification, ...prev]);
  }, []);

  const getNotificationsByType = useCallback((type) => {
    return notifications.filter(n => n.type === type);
  }, [notifications]);

  const getRecentNotifications = useCallback((limit = 5) => {
    return notifications
      .sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp))
      .slice(0, limit);
  }, [notifications]);

  const togglePanel = useCallback(() => {
    setIsOpen(prev => !prev);
  }, []);

  const closePanel = useCallback(() => {
    setIsOpen(false);
  }, []);

  const value = {
    notifications,
    unreadCount,
    isOpen,
    markAsRead,
    markAllAsRead,
    deleteNotification,
    clearAll,
    addNotification,
    getNotificationsByType,
    getRecentNotifications,
    togglePanel,
    closePanel
  };

  return (
    <NotificationContext.Provider value={value}>
      {children}
    </NotificationContext.Provider>
  );
};

// Hook for notification preferences
export const useNotificationPreferences = () => {
  const [preferences, setPreferences] = useState({
    email: true,
    push: true,
    sms: false,
    types: {
      siswa_baru: true,
      berkas_upload: true,
      mutasi: true,
      kelulusan: true,
      sistem: false
    }
  });

  const updatePreference = useCallback((key, value) => {
    setPreferences(prev => ({
      ...prev,
      [key]: value
    }));
  }, []);

  const updateTypePreference = useCallback((type, value) => {
    setPreferences(prev => ({
      ...prev,
      types: {
        ...prev.types,
        [type]: value
      }
    }));
  }, []);

  return {
    preferences,
    updatePreference,
    updateTypePreference
  };
};
