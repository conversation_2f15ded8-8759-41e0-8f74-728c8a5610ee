<?php
/**
 * Insert Default Kelas Data
 */

require_once 'app/models/Database.php';

echo "<h2>📚 Insert Default Kelas Data</h2>";

try {
    $db = new Database();
    
    // Check if kelas table has data
    $existingKelas = $db->fetchAll("SELECT COUNT(*) as count FROM kelas");
    $count = $existingKelas[0]['count'] ?? 0;
    
    echo "<p>Current kelas count: <strong>$count</strong></p>";
    
    if ($count == 0) {
        echo "<p style='color: orange;'>⚠️ No kelas data found. Inserting default data...</p>";
        
        $defaultKelas = [
            ['X-IPA-1', 'X', 'IPA', '2024/2025', 'Guru A'],
            ['X-IPA-2', 'X', 'IPA', '2024/2025', 'Guru B'],
            ['X-IPS-1', 'X', 'IPS', '2024/2025', 'Guru C'],
            ['XI-IPA-1', 'XI', 'IPA', '2024/2025', 'Guru D'],
            ['XI-IPA-2', 'XI', 'IPA', '2024/2025', 'Guru E'],
            ['XI-IPS-1', 'XI', 'IPS', '2024/2025', 'Guru F'],
            ['XII-IPA-1', 'XII', 'IPA', '2024/2025', 'Guru G'],
            ['XII-IPA-2', 'XII', 'IPA', '2024/2025', 'Guru H'],
            ['XII-IPS-1', 'XII', 'IPS', '2024/2025', 'Guru I']
        ];
        
        $sql = "INSERT INTO kelas (nama_kelas, tingkat, jurusan, tahun_pelajaran, wali_kelas, is_active) VALUES (?, ?, ?, ?, ?, 1)";
        
        foreach ($defaultKelas as $kelas) {
            $db->query($sql, $kelas);
            echo "<p style='color: green;'>✅ Inserted: {$kelas[0]}</p>";
        }
        
        echo "<p style='color: green; font-weight: bold;'>🎉 Default kelas data inserted successfully!</p>";
        
    } else {
        echo "<p style='color: blue;'>ℹ️ Kelas data already exists. Showing current data:</p>";
        
        $kelasList = $db->fetchAll("SELECT * FROM kelas ORDER BY tingkat, nama_kelas");
        
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr style='background: #f8f9fa;'>";
        echo "<th>ID</th><th>Nama Kelas</th><th>Tingkat</th><th>Jurusan</th><th>Tahun Pelajaran</th><th>Wali Kelas</th><th>Status</th>";
        echo "</tr>";
        
        foreach ($kelasList as $kelas) {
            $status = $kelas['is_active'] ? 'Aktif' : 'Tidak Aktif';
            $statusColor = $kelas['is_active'] ? 'green' : 'red';
            
            echo "<tr>";
            echo "<td>{$kelas['id']}</td>";
            echo "<td><strong>{$kelas['nama_kelas']}</strong></td>";
            echo "<td>{$kelas['tingkat']}</td>";
            echo "<td>{$kelas['jurusan']}</td>";
            echo "<td>{$kelas['tahun_pelajaran']}</td>";
            echo "<td>{$kelas['wali_kelas']}</td>";
            echo "<td style='color: $statusColor;'>$status</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
    // Update siswa yang belum punya kelas
    echo "<h3>🔄 Update Siswa Without Kelas</h3>";
    
    $siswaWithoutKelas = $db->fetchAll("SELECT id_siswa, nis, nama_lengkap FROM siswa WHERE kelas_id IS NULL OR kelas_id = 0");
    
    if (!empty($siswaWithoutKelas)) {
        echo "<p style='color: orange;'>⚠️ Found " . count($siswaWithoutKelas) . " siswa without kelas. Assigning to default kelas...</p>";
        
        // Get first available kelas
        $firstKelas = $db->fetch("SELECT id FROM kelas WHERE is_active = 1 ORDER BY tingkat, nama_kelas LIMIT 1");
        
        if ($firstKelas) {
            $defaultKelasId = $firstKelas['id'];
            
            foreach ($siswaWithoutKelas as $siswa) {
                $db->query("UPDATE siswa SET kelas_id = ? WHERE id_siswa = ?", [$defaultKelasId, $siswa['id_siswa']]);
                echo "<p style='color: green;'>✅ Updated siswa: {$siswa['nama_lengkap']} (NIS: {$siswa['nis']})</p>";
            }
        }
    } else {
        echo "<p style='color: green;'>✅ All siswa already have kelas assigned</p>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Error: " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "<pre>" . htmlspecialchars($e->getTraceAsString()) . "</pre>";
}

echo "<hr>";
echo "<p><a href='test_database_structure.php'>🔍 Test Database Structure</a></p>";
echo "<p><a href='public/siswa/edit/1'>✏️ Test Edit Siswa</a></p>";
echo "<p><a href='public/siswa'>📋 Student List</a></p>";
?>
