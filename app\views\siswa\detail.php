<?php
// Check if siswa data exists
if (!isset($siswa) || empty($siswa)) {
    echo '<div class="alert alert-danger">Data siswa tidak ditemukan.</div>';
    return;
}

// Load berkas model safely
try {
    require_once __DIR__ . '/../../models/Berkas.php';
    $berkas_model = new Berkas();
    $berkas = $berkas_model->getBySiswaId($siswa['id_siswa'] ?? $siswa['id'] ?? 0);
} catch (Exception $e) {
    $berkas = [];
}
?>

<div class="container-fluid">
    <!-- Page Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="h3 mb-0 text-gray-800">
                        <i class="bi bi-person-circle text-primary"></i>
                        Detail Siswa
                    </h1>
                    <p class="text-muted mb-0">Informasi lengkap siswa</p>
                </div>
                <div>
                    <a href="/siswa-app/public/siswa" class="btn btn-secondary">
                        <i class="bi bi-arrow-left"></i>
                        Kembali
                    </a>
                    <a href="/siswa-app/public/siswa/edit/<?= $siswa['id_siswa'] ?? $siswa['id'] ?>" class="btn btn-warning">
                        <i class="bi bi-pencil"></i>
                        Edit
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Student Information -->
        <div class="col-lg-8 mb-4">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="bi bi-info-circle"></i>
                        Informasi Siswa
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <table class="table table-borderless">
                                <tr>
                                    <td width="150"><strong>NIS:</strong></td>
                                    <td><?= htmlspecialchars($siswa['nis'] ?? 'N/A') ?></td>
                                </tr>
                                <tr>
                                    <td><strong>NISN:</strong></td>
                                    <td><?= htmlspecialchars($siswa['nisn'] ?? 'N/A') ?></td>
                                </tr>
                                <tr>
                                    <td><strong>Nama Lengkap:</strong></td>
                                    <td><?= htmlspecialchars($siswa['nama_lengkap'] ?? $siswa['nama'] ?? 'N/A') ?></td>
                                </tr>
                                <tr>
                                    <td><strong>Jenis Kelamin:</strong></td>
                                    <td>
                                        <?php
                                        $gender = $siswa['jenis_kelamin'] ?? 'L';
                                        $genderText = $gender === 'L' ? 'Laki-laki' : 'Perempuan';
                                        $genderIcon = $gender === 'L' ? 'bi-gender-male text-primary' : 'bi-gender-female text-danger';
                                        ?>
                                        <i class="bi <?= $genderIcon ?>"></i>
                                        <?= $genderText ?>
                                    </td>
                                </tr>
                                <tr>
                                    <td><strong>Tempat Lahir:</strong></td>
                                    <td><?= htmlspecialchars($siswa['tempat_lahir'] ?? 'N/A') ?></td>
                                </tr>
                                <tr>
                                    <td><strong>Tanggal Lahir:</strong></td>
                                    <td>
                                        <?php
                                        if (!empty($siswa['tanggal_lahir'])) {
                                            echo date('d F Y', strtotime($siswa['tanggal_lahir']));
                                        } else {
                                            echo 'N/A';
                                        }
                                        ?>
                                    </td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-6">
                            <table class="table table-borderless">
                                <tr>
                                    <td width="150"><strong>Kelas:</strong></td>
                                    <td>
                                        <?php if (!empty($siswa['nama_kelas'])): ?>
                                            <span class="badge bg-info fs-6">
                                                <?= htmlspecialchars($siswa['nama_kelas']) ?>
                                            </span>
                                        <?php else: ?>
                                            <span class="badge bg-secondary">Belum Ada Kelas</span>
                                        <?php endif; ?>
                                    </td>
                                </tr>
                                <tr>
                                    <td><strong>Tahun Masuk:</strong></td>
                                    <td><?= htmlspecialchars($siswa['tahun_masuk'] ?? 'N/A') ?></td>
                                </tr>
                                <tr>
                                    <td><strong>Status:</strong></td>
                                    <td>
                                        <?php
                                        $status = $siswa['status_siswa'] ?? 'aktif';
                                        $statusClass = [
                                            'aktif' => 'bg-success',
                                            'lulus' => 'bg-primary',
                                            'mutasi' => 'bg-warning',
                                            'dropout' => 'bg-danger'
                                        ];
                                        ?>
                                        <span class="badge <?= $statusClass[$status] ?? 'bg-secondary' ?> fs-6">
                                            <?= ucfirst($status) ?>
                                        </span>
                                    </td>
                                </tr>
                                <tr>
                                    <td><strong>Email:</strong></td>
                                    <td><?= htmlspecialchars($siswa['email'] ?? 'N/A') ?></td>
                                </tr>
                                <tr>
                                    <td><strong>No. Telepon:</strong></td>
                                    <td><?= htmlspecialchars($siswa['no_telepon'] ?? 'N/A') ?></td>
                                </tr>
                                <tr>
                                    <td><strong>Dibuat:</strong></td>
                                    <td>
                                        <?php
                                        if (!empty($siswa['created_at'])) {
                                            echo date('d F Y H:i', strtotime($siswa['created_at']));
                                        } else {
                                            echo 'N/A';
                                        }
                                        ?>
                                    </td>
                                </tr>
                            </table>
                        </div>
                    </div>

                    <?php if (!empty($siswa['alamat'])): ?>
                    <div class="row mt-3">
                        <div class="col-12">
                            <h6><strong>Alamat:</strong></h6>
                            <p class="text-muted"><?= nl2br(htmlspecialchars($siswa['alamat'])) ?></p>
                        </div>
                    </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <!-- Parent Information & Files -->
        <div class="col-lg-4">
            <!-- Parent Information -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="bi bi-people"></i>
                        Data Orang Tua
                    </h6>
                </div>
                <div class="card-body">
                    <table class="table table-borderless table-sm">
                        <tr>
                            <td><strong>Nama Ayah:</strong></td>
                            <td><?= htmlspecialchars($siswa['nama_ayah'] ?? 'N/A') ?></td>
                        </tr>
                        <tr>
                            <td><strong>Pekerjaan Ayah:</strong></td>
                            <td><?= htmlspecialchars($siswa['pekerjaan_ayah'] ?? 'N/A') ?></td>
                        </tr>
                        <tr>
                            <td><strong>Nama Ibu:</strong></td>
                            <td><?= htmlspecialchars($siswa['nama_ibu'] ?? 'N/A') ?></td>
                        </tr>
                        <tr>
                            <td><strong>Pekerjaan Ibu:</strong></td>
                            <td><?= htmlspecialchars($siswa['pekerjaan_ibu'] ?? 'N/A') ?></td>
                        </tr>
                    </table>
                </div>
            </div>

            <!-- Upload Files -->
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="bi bi-files"></i>
                        Berkas Siswa
                    </h6>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <a href="/siswa-app/public/upload/berkas/<?= $siswa['id_siswa'] ?? $siswa['id'] ?>" class="btn btn-primary btn-sm">
                            <i class="bi bi-upload"></i>
                            Upload Berkas
                        </a>
                    </div>

                    <?php if (!empty($berkas)): ?>
                        <div class="list-group">
                            <?php foreach ($berkas as $b): ?>
                            <div class="list-group-item">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div>
                                        <h6 class="mb-1"><?= htmlspecialchars($b['nama_berkas'] ?? 'File') ?></h6>
                                        <small class="text-muted">
                                            <?= ucfirst(str_replace('_', ' ', $b['jenis_berkas'] ?? 'lainnya')) ?>
                                        </small>
                                    </div>
                                    <div>
                                        <a href="/siswa-app/<?= $b['file_path'] ?? '#' ?>" target="_blank" class="btn btn-sm btn-outline-primary">
                                            <i class="bi bi-download"></i>
                                        </a>
                                    </div>
                                </div>
                            </div>
                            <?php endforeach; ?>
                        </div>
                    <?php else: ?>
                        <div class="text-center py-3">
                            <i class="bi bi-file-earmark" style="font-size: 2rem; color: #dee2e6;"></i>
                            <p class="text-muted mt-2 mb-0">Belum ada berkas</p>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>