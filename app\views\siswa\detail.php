<h2>Student Details</h2>
<p><strong>Name:</strong> <?= htmlspecialchars($siswa['nama']) ?></p>
<p><strong>Class:</strong> <?= htmlspecialchars($siswa['nama_kelas']) ?></p>
<p><strong>Created At:</strong> <?= $siswa['created_at'] ?></p>
<a href="/upload/berkas/<?= $siswa['id'] ?>">Upload File</a>
<h3>Uploaded Files</h3>
<?php
require_once __DIR__ . '/../../models/Berkas.php';
$berkas_model = new Berkas();
$berkas = $berkas_model->getBySiswaId($siswa['id']);
?>
<table border="1">
    <tr>
        <th>File Name</th>
        <th>Uploaded At</th>
    </tr>
    <?php foreach ($berkas as $b): ?>
    <tr>
        <td><a href="/<?= $b['file_path'] ?>" target="_blank"><?= htmlspecialchars($b['nama_berkas']) ?></a></td>
        <td><?= $b['uploaded_at'] ?></td>
    </tr>
    <?php endforeach; ?>
</table>