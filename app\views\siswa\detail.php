<?php
// Check if siswa data exists
if (!isset($siswa) || empty($siswa)) {
    echo '<div class="alert alert-danger">Data siswa tidak ditemukan.</div>';
    return;
}

// Load berkas model safely
try {
    require_once __DIR__ . '/../../models/Berkas.php';
    $berkas_model = new Berkas();
    $berkas = $berkas_model->getBySiswaId($siswa['id_siswa'] ?? $siswa['id'] ?? 0);
} catch (Exception $e) {
    $berkas = [];
}
?>

<div class="container-fluid">
    <!-- Page Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="h3 mb-0 text-gray-800">
                        <i class="bi bi-person-circle text-primary"></i>
                        Detail Siswa
                    </h1>
                    <p class="text-muted mb-0">Informasi lengkap siswa</p>
                </div>
                <div>
                    <a href="/siswa-app/public/siswa" class="btn btn-secondary">
                        <i class="bi bi-arrow-left"></i>
                        Kembali
                    </a>
                    <a href="/siswa-app/public/siswa/edit/<?= $siswa['id_siswa'] ?? $siswa['id'] ?>" class="btn btn-warning">
                        <i class="bi bi-pencil"></i>
                        Edit
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Student Information -->
        <div class="col-lg-8 mb-4">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="bi bi-info-circle"></i>
                        Informasi Siswa
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <table class="table table-borderless">
                                <tr>
                                    <td width="150"><strong>NIS:</strong></td>
                                    <td><?= htmlspecialchars($siswa['nis'] ?? 'N/A') ?></td>
                                </tr>
                                <tr>
                                    <td><strong>NISN:</strong></td>
                                    <td><?= htmlspecialchars($siswa['nisn'] ?? 'N/A') ?></td>
                                </tr>
                                <tr>
                                    <td><strong>Nama Lengkap:</strong></td>
                                    <td><?= htmlspecialchars($siswa['nama_lengkap'] ?? $siswa['nama'] ?? 'N/A') ?></td>
                                </tr>
                                <tr>
                                    <td><strong>Jenis Kelamin:</strong></td>
                                    <td>
                                        <?php
                                        $gender = $siswa['jenis_kelamin'] ?? 'L';
                                        $genderText = $gender === 'L' ? 'Laki-laki' : 'Perempuan';
                                        $genderIcon = $gender === 'L' ? 'bi-gender-male text-primary' : 'bi-gender-female text-danger';
                                        ?>
                                        <i class="bi <?= $genderIcon ?>"></i>
                                        <?= $genderText ?>
                                    </td>
                                </tr>
                                <tr>
                                    <td><strong>Tempat Lahir:</strong></td>
                                    <td><?= htmlspecialchars($siswa['tempat_lahir'] ?? 'N/A') ?></td>
                                </tr>
                                <tr>
                                    <td><strong>Tanggal Lahir:</strong></td>
                                    <td>
                                        <?php
                                        if (!empty($siswa['tanggal_lahir'])) {
                                            echo date('d F Y', strtotime($siswa['tanggal_lahir']));
                                        } else {
                                            echo 'N/A';
                                        }
                                        ?>
                                    </td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-6">
                            <table class="table table-borderless">
                                <tr>
                                    <td width="150"><strong>Kelas:</strong></td>
                                    <td>
                                        <?php if (!empty($siswa['nama_kelas'])): ?>
                                            <span class="badge bg-info fs-6">
                                                <?= htmlspecialchars($siswa['nama_kelas']) ?>
                                            </span>
                                        <?php else: ?>
                                            <span class="badge bg-secondary">Belum Ada Kelas</span>
                                        <?php endif; ?>
                                    </td>
                                </tr>
                                <tr>
                                    <td><strong>Tahun Masuk:</strong></td>
                                    <td><?= htmlspecialchars($siswa['tahun_masuk'] ?? 'N/A') ?></td>
                                </tr>
                                <tr>
                                    <td><strong>Status:</strong></td>
                                    <td>
                                        <?php
                                        $status = $siswa['status_siswa'] ?? 'aktif';
                                        $statusClass = [
                                            'aktif' => 'bg-success',
                                            'lulus' => 'bg-primary',
                                            'mutasi' => 'bg-warning',
                                            'dropout' => 'bg-danger'
                                        ];
                                        ?>
                                        <span class="badge <?= $statusClass[$status] ?? 'bg-secondary' ?> fs-6">
                                            <?= ucfirst($status) ?>
                                        </span>
                                    </td>
                                </tr>
                                <tr>
                                    <td><strong>Email:</strong></td>
                                    <td><?= htmlspecialchars($siswa['email'] ?? 'N/A') ?></td>
                                </tr>
                                <tr>
                                    <td><strong>No. Telepon:</strong></td>
                                    <td><?= htmlspecialchars($siswa['no_telepon'] ?? 'N/A') ?></td>
                                </tr>
                                <tr>
                                    <td><strong>Dibuat:</strong></td>
                                    <td>
                                        <?php
                                        if (!empty($siswa['created_at'])) {
                                            echo date('d F Y H:i', strtotime($siswa['created_at']));
                                        } else {
                                            echo 'N/A';
                                        }
                                        ?>
                                    </td>
                                </tr>
                            </table>
                        </div>
                    </div>

                    <?php if (!empty($siswa['alamat'])): ?>
                    <div class="row mt-3">
                        <div class="col-12">
                            <h6><strong>Alamat:</strong></h6>
                            <p class="text-muted"><?= nl2br(htmlspecialchars($siswa['alamat'])) ?></p>
                        </div>
                    </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <!-- Parent Information & Files -->
        <div class="col-lg-4">
            <!-- Parent Information -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="bi bi-people"></i>
                        Data Orang Tua
                    </h6>
                </div>
                <div class="card-body">
                    <table class="table table-borderless table-sm">
                        <tr>
                            <td><strong>Nama Ayah:</strong></td>
                            <td><?= htmlspecialchars($siswa['nama_ayah'] ?? 'N/A') ?></td>
                        </tr>
                        <tr>
                            <td><strong>Pekerjaan Ayah:</strong></td>
                            <td><?= htmlspecialchars($siswa['pekerjaan_ayah'] ?? 'N/A') ?></td>
                        </tr>
                        <tr>
                            <td><strong>Nama Ibu:</strong></td>
                            <td><?= htmlspecialchars($siswa['nama_ibu'] ?? 'N/A') ?></td>
                        </tr>
                        <tr>
                            <td><strong>Pekerjaan Ibu:</strong></td>
                            <td><?= htmlspecialchars($siswa['pekerjaan_ibu'] ?? 'N/A') ?></td>
                        </tr>
                    </table>
                </div>
            </div>

            <!-- Upload Files -->
            <div class="card shadow">
                <div class="card-header py-3 d-flex justify-content-between align-items-center">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="bi bi-files"></i>
                        Berkas Siswa
                    </h6>
                    <div class="btn-group">
                        <a href="/siswa-app/public/upload/berkas/<?= $siswa['id_siswa'] ?? $siswa['id'] ?>" class="btn btn-primary btn-sm">
                            <i class="bi bi-upload"></i>
                            Upload Berkas
                        </a>
                        <a href="/siswa-app/public/berkas/index/<?= $siswa['id_siswa'] ?? $siswa['id'] ?>" class="btn btn-outline-primary btn-sm">
                            <i class="bi bi-eye"></i>
                            Lihat Semua
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <?php if (!empty($berkas)): ?>
                        <?php
                        // Group berkas by category
                        $berkasModel = new Berkas();
                        $fileCategories = $berkasModel->getFileCategories();
                        $allowedTypes = $berkasModel->getAllowedTypes();

                        // Group files by category
                        $groupedBerkas = [];
                        foreach ($berkas as $b) {
                            $category = null;
                            foreach ($fileCategories as $catName => $types) {
                                if (array_key_exists($b['jenis_berkas'], $types)) {
                                    $category = $catName;
                                    break;
                                }
                            }
                            if (!$category) $category = 'Lainnya';
                            $groupedBerkas[$category][] = $b;
                        }
                        ?>

                        <?php foreach ($groupedBerkas as $categoryName => $categoryFiles): ?>
                            <div class="mb-4">
                                <h6 class="text-muted mb-3">
                                    <i class="bi bi-folder"></i>
                                    <?= htmlspecialchars($categoryName) ?>
                                    <span class="badge bg-secondary ms-2"><?= count($categoryFiles) ?></span>
                                </h6>

                                <div class="row">
                                    <?php foreach ($categoryFiles as $b): ?>
                                        <div class="col-md-6 mb-3">
                                            <div class="card border-0 bg-light h-100">
                                                <div class="card-body p-3">
                                                    <div class="d-flex justify-content-between align-items-start mb-2">
                                                        <div class="flex-grow-1">
                                                            <h6 class="card-title mb-1" style="font-size: 14px;">
                                                                <?= htmlspecialchars($b['nama_berkas'] ?? 'File') ?>
                                                            </h6>
                                                            <p class="card-text text-muted small mb-1">
                                                                <?= $allowedTypes[$b['jenis_berkas']] ?? ucfirst(str_replace('_', ' ', $b['jenis_berkas'])) ?>
                                                            </p>
                                                            <small class="text-muted">
                                                                <?= number_format(($b['ukuran_file'] ?? 0) / 1024, 1) ?> KB
                                                                • <?= date('d/m/Y', strtotime($b['created_at'] ?? 'now')) ?>
                                                            </small>
                                                        </div>
                                                        <div class="dropdown">
                                                            <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                                                                <i class="bi bi-three-dots"></i>
                                                            </button>
                                                            <ul class="dropdown-menu">
                                                                <li>
                                                                    <a class="dropdown-item" href="/siswa-app/public/upload/download/<?= $b['id'] ?>">
                                                                        <i class="bi bi-download"></i> Download
                                                                    </a>
                                                                </li>
                                                                <li>
                                                                    <a class="dropdown-item text-danger" href="/siswa-app/public/upload/delete/<?= $b['id'] ?>"
                                                                       onclick="return confirm('Yakin ingin menghapus file ini?')">
                                                                        <i class="bi bi-trash"></i> Hapus
                                                                    </a>
                                                                </li>
                                                            </ul>
                                                        </div>
                                                    </div>

                                                    <?php if (!empty($b['keterangan'])): ?>
                                                        <small class="text-muted">
                                                            <i class="bi bi-chat-text"></i>
                                                            <?= htmlspecialchars($b['keterangan']) ?>
                                                        </small>
                                                    <?php endif; ?>
                                                </div>
                                            </div>
                                        </div>
                                    <?php endforeach; ?>
                                </div>
                            </div>
                        <?php endforeach; ?>

                    <?php else: ?>
                        <div class="text-center py-5">
                            <i class="bi bi-file-earmark" style="font-size: 3rem; color: #dee2e6;"></i>
                            <h6 class="text-muted mt-3 mb-2">Belum ada berkas</h6>
                            <p class="text-muted small mb-3">Upload berkas pertama untuk siswa ini</p>
                            <a href="/siswa-app/public/upload/berkas/<?= $siswa['id_siswa'] ?? $siswa['id'] ?>" class="btn btn-primary">
                                <i class="bi bi-upload"></i>
                                Upload Berkas Sekarang
                            </a>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>