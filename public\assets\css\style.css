/* ===================================
   SISWA APP - CUSTOM STYLES
   Version: 2.0 with Security Enhancement
   =================================== */

/* ===== ROOT VARIABLES ===== */
:root {
    --primary-color: #4e73df;
    --secondary-color: #858796;
    --success-color: #1cc88a;
    --info-color: #36b9cc;
    --warning-color: #f6c23e;
    --danger-color: #e74a3b;
    --light-color: #f8f9fc;
    --dark-color: #5a5c69;
    --white-color: #ffffff;

    --border-radius: 0.35rem;
    --box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);
    --transition: all 0.3s ease;
}

/* ===== GLOBAL STYLES ===== */
body {
    font-family: 'Nunito', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Helvetica Neue', Arial, sans-serif;
    background-color: var(--light-color);
    color: var(--dark-color);
}

.text-primary { color: var(--primary-color) !important; }
.text-secondary { color: var(--secondary-color) !important; }
.text-success { color: var(--success-color) !important; }
.text-info { color: var(--info-color) !important; }
.text-warning { color: var(--warning-color) !important; }
.text-danger { color: var(--danger-color) !important; }

/* ===== NAVIGATION ===== */
.navbar-brand {
    font-weight: 800;
    font-size: 1.2rem;
}

.navbar-nav .nav-link {
    font-weight: 600;
    transition: var(--transition);
}

.navbar-nav .nav-link:hover {
    color: rgba(255, 255, 255, 0.8) !important;
}

.navbar-nav .nav-link.active {
    background-color: rgba(255, 255, 255, 0.1);
    border-radius: var(--border-radius);
}

/* ===== CARDS ===== */
.card {
    border: none;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    transition: var(--transition);
}

.card:hover {
    transform: translateY(-2px);
    box-shadow: 0 0.25rem 2rem 0 rgba(58, 59, 69, 0.2);
}

.card-header {
    background-color: var(--white-color);
    border-bottom: 1px solid #e3e6f0;
    font-weight: 700;
    color: var(--primary-color);
}

/* ===== BUTTONS ===== */
.btn {
    border-radius: var(--border-radius);
    font-weight: 600;
    transition: var(--transition);
}

.btn-primary {
    background: linear-gradient(45deg, var(--primary-color), #224abe);
    border: none;
}

.btn-primary:hover {
    background: linear-gradient(45deg, #224abe, #1e3a8a);
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.2);
}

.btn-success {
    background: linear-gradient(45deg, var(--success-color), #17a673);
    border: none;
}

.btn-info {
    background: linear-gradient(45deg, var(--info-color), #2c9faf);
    border: none;
}

.btn-warning {
    background: linear-gradient(45deg, var(--warning-color), #dda20a);
    border: none;
}

.btn-danger {
    background: linear-gradient(45deg, var(--danger-color), #c0392b);
    border: none;
}

/* ===== FORMS ===== */
.form-control {
    border-radius: var(--border-radius);
    border: 1px solid #d1d3e2;
    transition: var(--transition);
}

.form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(78, 115, 223, 0.25);
}

.form-label {
    font-weight: 600;
    color: var(--dark-color);
    margin-bottom: 0.5rem;
}

.input-group-text {
    background-color: #f8f9fc;
    border-color: #d1d3e2;
}

/* ===== ALERTS ===== */
.alert {
    border-radius: var(--border-radius);
    border: none;
    font-weight: 600;
}

.alert-success {
    background: linear-gradient(45deg, #d4edda, #c3e6cb);
    color: #155724;
}

.alert-danger {
    background: linear-gradient(45deg, #f8d7da, #f5c6cb);
    color: #721c24;
}

.alert-warning {
    background: linear-gradient(45deg, #fff3cd, #ffeaa7);
    color: #856404;
}

.alert-info {
    background: linear-gradient(45deg, #d1ecf1, #bee5eb);
    color: #0c5460;
}

/* ===== TABLES ===== */
.table {
    background-color: var(--white-color);
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: var(--box-shadow);
}

.table thead th {
    background-color: var(--primary-color);
    color: var(--white-color);
    font-weight: 700;
    border: none;
    padding: 1rem;
}

.table tbody tr {
    transition: var(--transition);
}

.table tbody tr:hover {
    background-color: #f8f9fc;
}

.table tbody td {
    padding: 1rem;
    border-color: #e3e6f0;
}