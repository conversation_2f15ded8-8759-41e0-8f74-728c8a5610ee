<?php
require_once __DIR__ . '/../models/Siswa.php';
require_once __DIR__ . '/../models/Kelas.php';

class SiswaController {
    private $siswa;
    private $kelas;

    public function __construct() {
        $this->siswa = new Siswa();
        $this->kelas = new Kelas();
    }

    public function index() {
        $data['siswa'] = $this->siswa->getAll();
        $this->view('siswa/list', $data);
    }

    public function create() {
        $data['kelas'] = $this->kelas->getAll();
        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            $nama = $_POST['nama'] ?? '';
            $kelas_id = $_POST['kelas_id'] ?? 0;
            if ($nama && $kelas_id) {
                $this->siswa->create($nama, $kelas_id);
                header('Location: /siswa');
                exit;
            }
        }
        $this->view('siswa/form', $data);
    }

    public function edit($id) {
        $data['siswa'] = $this->siswa->getById($id);
        $data['kelas'] = $this->kelas->getAll();
        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            $nama = $_POST['nama'] ?? '';
            $kelas_id = $_POST['kelas_id'] ?? 0;
            if ($nama && $kelas_id) {
                $this->siswa->update($id, $nama, $kelas_id);
                header('Location: /siswa');
                exit;
            }
        }
        $this->view('siswa/form', $data);
    }

    public function delete($id) {
        $this->siswa->delete($id);
        header('Location: /siswa');
        exit;
    }

    public function detail($id) {
        $data['siswa'] = $this->siswa->getById($id);
        $this->view('siswa/detail', $data);
    }

    private function view($path, $data = []) {
        extract($data);
        require_once __DIR__ . '/../views/layouts/header.php';
        require_once __DIR__ . '/../views/' . $path . '.php';
        require_once __DIR__ . '/../views/layouts/footer.php';
    }
}
?>