<?php
require_once __DIR__ . '/../models/Siswa.php';
require_once __DIR__ . '/../models/Kelas.php';
require_once __DIR__ . '/../helpers/Security.php';

class SiswaController {
    private $siswa;
    private $kelas;

    public function __construct() {
        $this->siswa = new Siswa();
        $this->kelas = new Kelas();
    }

    public function index() {
        $data['siswa'] = $this->siswa->getAll();
        $this->view('siswa/list', $data);
    }

    public function create() {
        $data['kelas'] = $this->kelas->getAll();
        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            $nama = $_POST['nama'] ?? '';
            $kelas_id = $_POST['kelas_id'] ?? 0;
            if ($nama && $kelas_id) {
                $this->siswa->create($nama, $kelas_id);
                header('Location: /siswa');
                exit;
            }
        }
        $this->view('siswa/form', $data);
    }

    public function edit($id) {
        Security::requireAuth();

        // Get student data
        $siswa = $this->siswa->getById($id);
        if (!$siswa) {
            $_SESSION['error'] = 'Data siswa tidak ditemukan';
            header('Location: /siswa-app/public/siswa');
            exit;
        }

        // Handle form submission
        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            try {
                // Validate CSRF token
                if (!Security::validateCSRFToken($_POST['csrf_token'] ?? '')) {
                    throw new Exception('Token keamanan tidak valid');
                }

                // Get form data
                $updateData = [
                    'nis' => $_POST['nis'] ?? '',
                    'nisn' => $_POST['nisn'] ?? '',
                    'nama_lengkap' => $_POST['nama_lengkap'] ?? '',
                    'jenis_kelamin' => $_POST['jenis_kelamin'] ?? '',
                    'tempat_lahir' => $_POST['tempat_lahir'] ?? '',
                    'tanggal_lahir' => $_POST['tanggal_lahir'] ?? '',
                    'kelas_id' => $_POST['kelas_id'] ?? '',
                    'tahun_masuk' => $_POST['tahun_masuk'] ?? '',
                    'status_siswa' => $_POST['status_siswa'] ?? 'Aktif',
                    'email' => $_POST['email'] ?? '',
                    'no_telepon' => $_POST['no_telepon'] ?? '',
                    'alamat' => $_POST['alamat'] ?? ''
                ];

                // Validate required fields
                if (empty($updateData['nis'])) {
                    throw new Exception('NIS harus diisi');
                }
                if (empty($updateData['nama_lengkap'])) {
                    throw new Exception('Nama lengkap harus diisi');
                }
                if (empty($updateData['jenis_kelamin'])) {
                    throw new Exception('Jenis kelamin harus dipilih');
                }
                if (empty($updateData['kelas_id'])) {
                    throw new Exception('Kelas harus dipilih');
                }

                // Update student data
                $result = $this->siswa->updateComplete($id, $updateData);

                if ($result) {
                    $_SESSION['success'] = 'Data siswa berhasil diperbarui';
                    header('Location: /siswa-app/public/siswa/detail/' . $id);
                    exit;
                } else {
                    throw new Exception('Gagal memperbarui data siswa');
                }

            } catch (Exception $e) {
                $_SESSION['error'] = $e->getMessage();
            }
        }

        // Get kelas list for dropdown
        $kelasList = $this->kelas->getAll();

        $data = [
            'title' => 'Edit Siswa - ' . $siswa['nama_lengkap'],
            'siswa' => $siswa,
            'kelas_list' => $kelasList,
            'csrf_token' => Security::generateCSRFToken()
        ];

        $this->view('siswa/edit', $data);
    }

    public function delete($id) {
        $this->siswa->delete($id);
        header('Location: /siswa');
        exit;
    }

    public function detail($id) {
        Security::requireAuth();

        $siswa = $this->siswa->getById($id);
        if (!$siswa) {
            $_SESSION['error'] = 'Data siswa tidak ditemukan';
            header('Location: /siswa-app/public/siswa');
            exit;
        }

        // Get berkas for this student
        require_once __DIR__ . '/../models/Berkas.php';
        $berkasModel = new Berkas();
        $berkas = $berkasModel->getBySiswaId($id);

        $data = [
            'title' => 'Detail Siswa - ' . $siswa['nama_lengkap'],
            'siswa' => $siswa,
            'berkas' => $berkas
        ];

        $this->view('siswa/detail', $data);
    }

    private function view($path, $data = []) {
        extract($data);
        require_once __DIR__ . '/../views/layouts/header.php';
        require_once __DIR__ . '/../views/' . $path . '.php';
        require_once __DIR__ . '/../views/layouts/footer.php';
    }
}
?>