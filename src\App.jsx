import React from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { AuthProvider, useAuth } from './contexts/AuthContext';
import { ToastProvider } from './contexts/ToastContext';
import { NotificationProvider } from './contexts/NotificationContext';
import { ThemeProvider } from './contexts/ThemeContext';
import './i18n'; // Initialize i18n
import ProtectedRoute from './components/common/ProtectedRoute';
import LoginPage from './components/pages/LoginPage';
import DashboardPage from './components/pages/DashboardPage';
import SiswaListPage from './components/pages/SiswaListPage';
import SiswaDetailPage from './components/pages/SiswaDetailPage';
import UploadBerkasPage from './components/pages/UploadBerkasPage';
import SettingsPage from './components/pages/SettingsPage';
import ReportsPage from './components/pages/ReportsPage';
import LoadingSpinner from './components/common/LoadingSpinner';

// Component untuk handle redirect logic
const AppRoutes = () => {
  const { isAuthenticated, loading } = useAuth();

  if (loading) {
    return <LoadingSpinner text="Memuat aplikasi..." />;
  }

  return (
    <Routes>
      <Route
        path="/login"
        element={
          isAuthenticated() ? <Navigate to="/" replace /> : <LoginPage />
        }
      />
      <Route
        path="/"
        element={
          <ProtectedRoute>
            <DashboardPage />
          </ProtectedRoute>
        }
      />
      <Route
        path="/siswa"
        element={
          <ProtectedRoute>
            <SiswaListPage />
          </ProtectedRoute>
        }
      />
      <Route
        path="/siswa/:id"
        element={
          <ProtectedRoute>
            <SiswaDetailPage />
          </ProtectedRoute>
        }
      />
      <Route
        path="/upload-berkas/:id"
        element={
          <ProtectedRoute roles={['admin', 'staff']}>
            <UploadBerkasPage />
          </ProtectedRoute>
        }
      />
      <Route
        path="/settings"
        element={
          <ProtectedRoute>
            <SettingsPage />
          </ProtectedRoute>
        }
      />
      <Route
        path="/reports"
        element={
          <ProtectedRoute roles={['admin', 'staff']}>
            <ReportsPage />
          </ProtectedRoute>
        }
      />
      <Route path="*" element={<Navigate to="/" replace />} />
    </Routes>
  );
};

function App() {
  return (
    <Router>
      <AuthProvider>
        <ToastProvider>
          <NotificationProvider>
            <ThemeProvider>
              <AppRoutes />
            </ThemeProvider>
          </NotificationProvider>
        </ToastProvider>
      </AuthProvider>
    </Router>
  );
}

export default App;