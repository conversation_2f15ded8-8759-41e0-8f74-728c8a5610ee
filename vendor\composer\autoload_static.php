<?php

// autoload_static.php @generated by Composer

namespace Composer\Autoload;

class ComposerStaticInitb7dd77f98c0aa81feef54d903eecad45
{
    public static $prefixLengthsPsr4 = array (
        'A' => 
        array (
            'App\\' => 4,
        ),
    );

    public static $prefixDirsPsr4 = array (
        'App\\' => 
        array (
            0 => __DIR__ . '/../..' . '/app',
        ),
    );

    public static $classMap = array (
        'Composer\\InstalledVersions' => __DIR__ . '/..' . '/composer/InstalledVersions.php',
    );

    public static function getInitializer(ClassLoader $loader)
    {
        return \Closure::bind(function () use ($loader) {
            $loader->prefixLengthsPsr4 = ComposerStaticInitb7dd77f98c0aa81feef54d903eecad45::$prefixLengthsPsr4;
            $loader->prefixDirsPsr4 = ComposerStaticInitb7dd77f98c0aa81feef54d903eecad45::$prefixDirsPsr4;
            $loader->classMap = ComposerStaticInitb7dd77f98c0aa81feef54d903eecad45::$classMap;

        }, null, ClassLoader::class);
    }
}
