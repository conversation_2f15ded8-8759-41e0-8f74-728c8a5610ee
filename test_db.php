<?php
/**
 * Database Connection Test
 * Run this file to test database connectivity
 */

require_once 'app/config/db.php';

echo "<h2>Database Connection Test</h2>";

try {
    $pdo = getDBConnection();
    echo "<p style='color: green;'>✅ Database connection successful!</p>";
    
    // Test if users table exists
    $stmt = $pdo->query("SHOW TABLES LIKE 'users'");
    if ($stmt->rowCount() > 0) {
        echo "<p style='color: green;'>✅ Users table exists!</p>";
        
        // Check if admin user exists
        $stmt = $pdo->prepare("SELECT * FROM users WHERE username = 'admin'");
        $stmt->execute();
        if ($stmt->rowCount() > 0) {
            echo "<p style='color: green;'>✅ Admin user exists!</p>";
        } else {
            echo "<p style='color: orange;'>⚠️ Admin user not found. Please import the database schema.</p>";
        }
    } else {
        echo "<p style='color: red;'>❌ Users table not found. Please import the database schema.</p>";
        echo "<p>Run this command:</p>";
        echo "<code>mysql -u root -p siswa_app < database/schema_enhanced.sql</code>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Database connection failed: " . $e->getMessage() . "</p>";
    echo "<p>Please check:</p>";
    echo "<ul>";
    echo "<li>MySQL server is running</li>";
    echo "<li>Database 'siswa_app' exists</li>";
    echo "<li>Database credentials in app/config/db.php are correct</li>";
    echo "</ul>";
}

echo "<hr>";
echo "<p><a href='public/'>Go to Application</a></p>";
?>
