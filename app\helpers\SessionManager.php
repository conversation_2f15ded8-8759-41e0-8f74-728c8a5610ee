<?php
require_once __DIR__ . '/../models/Database.php';

class SessionManager {
    private $db;
    private $sessionLifetime = 3600; // 1 hour
    
    public function __construct() {
        $this->db = new Database();
        $this->configureSession();
    }
    
    /**
     * Configure secure session settings
     */
    private function configureSession() {
        // Set secure session parameters
        ini_set('session.cookie_httponly', 1);
        ini_set('session.cookie_secure', isset($_SERVER['HTTPS']));
        ini_set('session.use_strict_mode', 1);
        ini_set('session.cookie_samesite', 'Strict');
        
        // Set session lifetime
        ini_set('session.gc_maxlifetime', $this->sessionLifetime);
        
        // Custom session handler
        session_set_save_handler(
            [$this, 'sessionOpen'],
            [$this, 'sessionClose'],
            [$this, 'sessionRead'],
            [$this, 'sessionWrite'],
            [$this, 'sessionDestroy'],
            [$this, 'sessionGC']
        );
    }
    
    /**
     * Start session
     */
    public function startSession() {
        if (session_status() === PHP_SESSION_NONE) {
            session_start();
            
            // Regenerate session ID periodically for security
            if (!isset($_SESSION['last_regeneration'])) {
                $this->regenerateSessionId();
            } elseif (time() - $_SESSION['last_regeneration'] > 300) { // 5 minutes
                $this->regenerateSessionId();
            }
        }
    }
    
    /**
     * Regenerate session ID
     */
    public function regenerateSessionId() {
        session_regenerate_id(true);
        $_SESSION['last_regeneration'] = time();
    }
    
    /**
     * Login user
     */
    public function loginUser($user) {
        $this->startSession();
        
        // Store user data in session
        $_SESSION['user_id'] = $user['id'];
        $_SESSION['username'] = $user['username'];
        $_SESSION['user_role'] = $user['role'];
        $_SESSION['nama_lengkap'] = $user['nama_lengkap'];
        $_SESSION['login_time'] = time();
        $_SESSION['last_activity'] = time();
        
        // Regenerate session ID for security
        $this->regenerateSessionId();
        
        // Store session in database
        $this->storeSessionInDB($user['id']);
    }
    
    /**
     * Logout user
     */
    public function logoutUser() {
        $this->startSession();
        
        if (isset($_SESSION['user_id'])) {
            // Remove session from database
            $this->removeSessionFromDB();
            
            // Log security event
            Security::logSecurityEvent('user_logout', ['user_id' => $_SESSION['user_id']]);
        }
        
        // Clear session data
        $_SESSION = [];
        
        // Delete session cookie
        if (isset($_COOKIE[session_name()])) {
            setcookie(session_name(), '', time() - 3600, '/');
        }
        
        // Destroy session
        session_destroy();
    }
    
    /**
     * Check if session is valid
     */
    public function isValidSession() {
        $this->startSession();
        
        if (!isset($_SESSION['user_id'])) {
            return false;
        }
        
        // Check session timeout
        if (isset($_SESSION['last_activity']) && (time() - $_SESSION['last_activity']) > $this->sessionLifetime) {
            $this->logoutUser();
            return false;
        }
        
        // Update last activity
        $_SESSION['last_activity'] = time();
        
        // Verify session in database
        return $this->verifySessionInDB();
    }
    
    /**
     * Store session in database
     */
    private function storeSessionInDB($userId) {
        $sessionId = session_id();
        $ipAddress = $_SERVER['REMOTE_ADDR'] ?? '';
        $userAgent = $_SERVER['HTTP_USER_AGENT'] ?? '';
        $payload = json_encode($_SESSION);
        
        // Remove old sessions for this user
        $this->db->query(
            "DELETE FROM sessions WHERE user_id = ?",
            [$userId]
        );
        
        // Insert new session
        $this->db->query(
            "INSERT INTO sessions (id, user_id, ip_address, user_agent, payload, last_activity, created_at) 
             VALUES (?, ?, ?, ?, ?, NOW(), NOW())",
            [$sessionId, $userId, $ipAddress, $userAgent, $payload]
        );
    }
    
    /**
     * Remove session from database
     */
    private function removeSessionFromDB() {
        $sessionId = session_id();
        $this->db->query("DELETE FROM sessions WHERE id = ?", [$sessionId]);
    }
    
    /**
     * Verify session in database
     */
    private function verifySessionInDB() {
        $sessionId = session_id();
        $session = $this->db->fetch(
            "SELECT * FROM sessions WHERE id = ? AND user_id = ?",
            [$sessionId, $_SESSION['user_id']]
        );
        
        if (!$session) {
            return false;
        }
        
        // Update last activity in database
        $this->db->query(
            "UPDATE sessions SET last_activity = NOW(), payload = ? WHERE id = ?",
            [json_encode($_SESSION), $sessionId]
        );
        
        return true;
    }
    
    /**
     * Session handler: Open
     */
    public function sessionOpen($savePath, $sessionName) {
        return true;
    }
    
    /**
     * Session handler: Close
     */
    public function sessionClose() {
        return true;
    }
    
    /**
     * Session handler: Read
     */
    public function sessionRead($sessionId) {
        $session = $this->db->fetch(
            "SELECT payload FROM sessions WHERE id = ?",
            [$sessionId]
        );
        
        return $session ? $session['payload'] : '';
    }
    
    /**
     * Session handler: Write
     */
    public function sessionWrite($sessionId, $data) {
        if (!isset($_SESSION['user_id'])) {
            return true;
        }
        
        $ipAddress = $_SERVER['REMOTE_ADDR'] ?? '';
        $userAgent = $_SERVER['HTTP_USER_AGENT'] ?? '';
        
        $this->db->query(
            "INSERT INTO sessions (id, user_id, ip_address, user_agent, payload, last_activity, created_at) 
             VALUES (?, ?, ?, ?, ?, NOW(), NOW()) 
             ON DUPLICATE KEY UPDATE 
             payload = VALUES(payload), 
             last_activity = NOW()",
            [$sessionId, $_SESSION['user_id'], $ipAddress, $userAgent, $data]
        );
        
        return true;
    }
    
    /**
     * Session handler: Destroy
     */
    public function sessionDestroy($sessionId) {
        $this->db->query("DELETE FROM sessions WHERE id = ?", [$sessionId]);
        return true;
    }
    
    /**
     * Session handler: Garbage Collection
     */
    public function sessionGC($maxLifetime) {
        $this->db->query(
            "DELETE FROM sessions WHERE last_activity < DATE_SUB(NOW(), INTERVAL ? SECOND)",
            [$maxLifetime]
        );
        return true;
    }
    
    /**
     * Get current user data
     */
    public function getCurrentUser() {
        $this->startSession();
        
        if (!$this->isValidSession()) {
            return null;
        }
        
        return [
            'id' => $_SESSION['user_id'],
            'username' => $_SESSION['username'],
            'role' => $_SESSION['user_role'],
            'nama_lengkap' => $_SESSION['nama_lengkap']
        ];
    }
    
    /**
     * Get all active sessions for a user
     */
    public function getUserSessions($userId) {
        return $this->db->fetchAll(
            "SELECT id, ip_address, user_agent, last_activity, created_at 
             FROM sessions WHERE user_id = ? 
             ORDER BY last_activity DESC",
            [$userId]
        );
    }
    
    /**
     * Terminate all sessions for a user
     */
    public function terminateUserSessions($userId) {
        $this->db->query("DELETE FROM sessions WHERE user_id = ?", [$userId]);
        Security::logSecurityEvent('all_sessions_terminated', ['user_id' => $userId]);
    }
}
