<?php
/**
 * Debug Dashboard Script
 */

error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h2>🔍 Debug Dashboard</h2>";

try {
    echo "<p>✅ Starting debug...</p>";
    
    // Load dependencies
    echo "<p>📦 Loading dependencies...</p>";
    require_once 'app/models/Database.php';
    echo "<p>✅ Database class loaded</p>";
    
    require_once 'app/helpers/Security.php';
    echo "<p>✅ Security class loaded</p>";
    
    require_once 'app/helpers/SimpleSessionManager.php';
    echo "<p>✅ SimpleSessionManager class loaded</p>";
    
    // Initialize session manager
    echo "<p>🔧 Initializing session manager...</p>";
    $sessionManager = new SimpleSessionManager();
    echo "<p>✅ Session manager initialized</p>";
    
    // Load models
    echo "<p>📊 Loading models...</p>";
    require_once 'app/models/User.php';
    echo "<p>✅ User model loaded</p>";
    
    require_once 'app/models/Siswa.php';
    echo "<p>✅ Siswa model loaded</p>";
    
    require_once 'app/models/Kelas.php';
    echo "<p>✅ Kelas model loaded</p>";
    
    require_once 'app/models/Berkas.php';
    echo "<p>✅ Berkas model loaded</p>";
    
    // Load controllers
    echo "<p>🎮 Loading controllers...</p>";
    require_once 'app/controllers/DashboardController.php';
    echo "<p>✅ DashboardController loaded</p>";
    
    // Test database connection
    echo "<p>🗄️ Testing database connection...</p>";
    $db = new Database();
    $testQuery = $db->fetch("SELECT 1 as test");
    if ($testQuery) {
        echo "<p>✅ Database connection successful</p>";
    } else {
        echo "<p>❌ Database connection failed</p>";
    }
    
    // Test models
    echo "<p>🧪 Testing models...</p>";
    
    $siswaModel = new Siswa();
    $allSiswa = $siswaModel->getAll();
    echo "<p>✅ Siswa model works - Found " . count($allSiswa) . " students</p>";
    
    $kelasModel = new Kelas();
    $allKelas = $kelasModel->getAll();
    echo "<p>✅ Kelas model works - Found " . count($allKelas) . " classes</p>";
    
    $berkasModel = new Berkas();
    echo "<p>✅ Berkas model loaded</p>";
    
    // Test dashboard controller
    echo "<p>📊 Testing dashboard controller...</p>";
    $dashboardController = new DashboardController();
    echo "<p>✅ DashboardController instantiated successfully</p>";
    
    // Test authentication
    echo "<p>🔐 Testing authentication...</p>";
    $isAuth = Security::isAuthenticated();
    echo "<p>Authentication status: " . ($isAuth ? "✅ Authenticated" : "❌ Not authenticated") . "</p>";
    
    if ($isAuth) {
        $currentUser = $sessionManager->getCurrentUser();
        echo "<p>Current user: " . htmlspecialchars($currentUser['nama_lengkap'] ?? 'Unknown') . "</p>";
    }
    
    echo "<p style='color: green; font-weight: bold;'>🎉 All tests passed! Dashboard should work.</p>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Error: " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "<p style='color: red;'>📍 File: " . $e->getFile() . " Line: " . $e->getLine() . "</p>";
    echo "<pre style='background: #f8f8f8; padding: 10px; border-radius: 5px;'>" . htmlspecialchars($e->getTraceAsString()) . "</pre>";
}

echo "<hr>";
echo "<p><a href='simple_login.php'>🔐 Simple Login</a></p>";
echo "<p><a href='public/'>🏠 Main Application</a></p>";
echo "<p><a href='public/dashboard'>📊 Dashboard (Direct)</a></p>";
?>
