    </main>

    <!-- Footer -->
    <footer class="bg-dark text-light py-4 mt-5">
        <div class="container">
            <div class="row">
                <div class="col-md-6">
                    <h6 class="fw-bold">Sistem Informasi Akademik Siswa</h6>
                    <p class="mb-0 text-muted">Mengelola data siswa dengan mudah dan aman</p>
                </div>
                <div class="col-md-6 text-md-end">
                    <p class="mb-0 text-muted">© 2025 SISWA APP. All rights reserved.</p>
                    <small class="text-muted">Version 2.0 with Security Enhancement</small>
                </div>
            </div>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <!-- Custom JS -->
    <script src="/assets/js/main.js"></script>

    <!-- Security: Auto logout on idle -->
    <script>
        let idleTimer;
        const idleTime = 30 * 60 * 1000; // 30 minutes

        function resetIdleTimer() {
            clearTimeout(idleTimer);
            idleTimer = setTimeout(() => {
                if (confirm('Sesi Anda akan berakhir karena tidak aktif. Klik OK untuk melanjutkan atau Cancel untuk logout.')) {
                    resetIdleTimer();
                } else {
                    window.location.href = '/logout';
                }
            }, idleTime);
        }

        // Reset timer on user activity
        document.addEventListener('mousemove', resetIdleTimer);
        document.addEventListener('keypress', resetIdleTimer);
        document.addEventListener('click', resetIdleTimer);

        // Start timer
        resetIdleTimer();
    </script>
</body>
</html>