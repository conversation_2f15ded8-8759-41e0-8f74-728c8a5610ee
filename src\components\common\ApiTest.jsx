import React, { useState } from 'react';
import api from '../../services/api';

const ApiTest = () => {
  const [status, setStatus] = useState('idle');
  const [result, setResult] = useState(null);
  const [error, setError] = useState(null);

  const testConnection = async () => {
    setStatus('testing');
    setError(null);
    setResult(null);

    try {
      // Test health endpoint
      const response = await fetch('http://localhost:5000/health');
      const data = await response.json();
      
      setResult(data);
      setStatus('success');
    } catch (err) {
      setError(err.message);
      setStatus('error');
    }
  };

  const testLogin = async () => {
    setStatus('testing');
    setError(null);
    setResult(null);

    try {
      const response = await api.post('/auth/login', {
        username: 'admin',
        password: 'admin123'
      });
      
      setResult(response.data);
      setStatus('success');
    } catch (err) {
      setError(err.response?.data?.message || err.message);
      setStatus('error');
    }
  };

  const testSiswa = async () => {
    setStatus('testing');
    setError(null);
    setResult(null);

    try {
      const response = await api.getSiswaList();
      setResult(response.data);
      setStatus('success');
    } catch (err) {
      setError(err.response?.data?.message || err.message);
      setStatus('error');
    }
  };

  return (
    <div className="bg-white p-6 rounded-lg shadow-md">
      <h3 className="text-lg font-semibold mb-4">API Connection Test</h3>
      
      <div className="space-y-4">
        <div className="flex space-x-4">
          <button
            onClick={testConnection}
            disabled={status === 'testing'}
            className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 disabled:opacity-50"
          >
            Test Health
          </button>
          
          <button
            onClick={testLogin}
            disabled={status === 'testing'}
            className="px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700 disabled:opacity-50"
          >
            Test Login
          </button>
          
          <button
            onClick={testSiswa}
            disabled={status === 'testing'}
            className="px-4 py-2 bg-purple-600 text-white rounded hover:bg-purple-700 disabled:opacity-50"
          >
            Test Siswa API
          </button>
        </div>

        {status === 'testing' && (
          <div className="text-blue-600">Testing API connection...</div>
        )}

        {status === 'success' && result && (
          <div className="bg-green-50 border border-green-200 rounded p-4">
            <h4 className="text-green-800 font-medium mb-2">Success!</h4>
            <pre className="text-sm text-green-700 overflow-auto">
              {JSON.stringify(result, null, 2)}
            </pre>
          </div>
        )}

        {status === 'error' && error && (
          <div className="bg-red-50 border border-red-200 rounded p-4">
            <h4 className="text-red-800 font-medium mb-2">Error!</h4>
            <p className="text-red-700">{error}</p>
          </div>
        )}
      </div>
    </div>
  );
};

export default ApiTest;
