<?php
require_once __DIR__ . '/Database.php';

class Berkas {
    private $db;

    public function __construct() {
        $this->db = new Database();
    }

    public function getBySiswaId($siswa_id) {
        return $this->db->fetchAll("SELECT * FROM berkas WHERE siswa_id = ?", [$siswa_id]);
    }

    public function create($siswa_id, $nama_berkas, $file_path) {
        $this->db->query("INSERT INTO berkas (siswa_id, nama_berkas, file_path) VALUES (?, ?, ?)", [$siswa_id, $nama_berkas, $file_path]);
        return $this->db->lastInsertId();
    }
}
?>