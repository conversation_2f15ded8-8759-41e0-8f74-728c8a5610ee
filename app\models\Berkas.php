<?php
require_once __DIR__ . '/Database.php';

class Berkas {
    private $db;

    public function __construct() {
        $this->db = new Database();
    }

    /**
     * Get files by student ID
     */
    public function getBySiswaId($siswa_id) {
        try {
            return $this->db->fetchAll("
                SELECT * FROM berkas
                WHERE siswa_id = ?
                ORDER BY jenis_berkas, created_at DESC
            ", [$siswa_id]);
        } catch (Exception $e) {
            return [];
        }
    }

    /**
     * Create new file record
     */
    public function create($data) {
        $sql = "INSERT INTO berkas (
            siswa_id, jenis_berkas, nama_berkas, nama_file_asli,
            nama_file_sistem, ukuran_file, mime_type, file_path,
            file_hash, keterangan, uploaded_by
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";

        $params = [
            $data['siswa_id'],
            $data['jenis_berkas'],
            $data['nama_berkas'],
            $data['nama_file_asli'],
            $data['nama_file_sistem'],
            $data['ukuran_file'],
            $data['mime_type'],
            $data['file_path'],
            $data['file_hash'],
            $data['keterangan'] ?? null,
            $data['uploaded_by'] ?? 1
        ];

        $this->db->query($sql, $params);
        return $this->db->lastInsertId();
    }

    /**
     * Delete file record
     */
    public function delete($id) {
        $this->db->query("DELETE FROM berkas WHERE id = ?", [$id]);
        return true;
    }

    /**
     * Get file by ID
     */
    public function getById($id) {
        return $this->db->fetch("SELECT * FROM berkas WHERE id = ?", [$id]);
    }

    /**
     * Get allowed file types with categories
     */
    public function getFileCategories() {
        return [
            'Dokumen Identitas' => [
                'kartu_keluarga' => 'Kartu Keluarga',
                'akta_lahir' => 'Akta Kelahiran'
            ],
            'Rapor' => [
                'rapor_kelas_x' => 'Rapor Kelas X',
                'rapor_kelas_xi' => 'Rapor Kelas XI',
                'rapor_kelas_xii' => 'Rapor Kelas XII'
            ],
            'Ijazah' => [
                'ijazah_sd' => 'Ijazah SD/MI',
                'ijazah_smp' => 'Ijazah SMP/MTs',
                'ijazah_sma' => 'Ijazah SMA/SMK/MA'
            ],
            'Dokumen Pendukung' => [
                'foto_siswa' => 'Foto Siswa',
                'surat_keterangan_sehat' => 'Surat Keterangan Sehat',
                'surat_kelakuan_baik' => 'Surat Kelakuan Baik',
                'piagam_prestasi' => 'Piagam Prestasi',
                'lainnya' => 'Lainnya'
            ]
        ];
    }

    /**
     * Get flat list of allowed file types
     */
    public function getAllowedTypes() {
        $types = [];
        foreach ($this->getFileCategories() as $category => $items) {
            $types = array_merge($types, $items);
        }
        return $types;
    }

    /**
     * Get allowed MIME types
     */
    public function getAllowedMimeTypes() {
        return [
            'application/pdf',
            'image/jpeg',
            'image/jpg',
            'image/png',
            'image/gif',
            'application/msword',
            'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
        ];
    }

    /**
     * Get max file size (in bytes)
     */
    public function getMaxFileSize() {
        return 5 * 1024 * 1024; // 5MB
    }

    /**
     * Validate file upload
     */
    public function validateFile($file, $jenis_berkas) {
        $errors = [];

        // Check if file was uploaded
        if (!isset($file['tmp_name']) || empty($file['tmp_name'])) {
            $errors[] = 'File tidak ditemukan';
            return $errors;
        }

        // Check file size
        if ($file['size'] > $this->getMaxFileSize()) {
            $errors[] = 'Ukuran file terlalu besar (maksimal 5MB)';
        }

        // Check MIME type
        $finfo = finfo_open(FILEINFO_MIME_TYPE);
        $mimeType = finfo_file($finfo, $file['tmp_name']);
        finfo_close($finfo);

        if (!in_array($mimeType, $this->getAllowedMimeTypes())) {
            $errors[] = 'Tipe file tidak diizinkan. Gunakan PDF, JPG, PNG, atau DOC';
        }

        // Check file extension
        $allowedExtensions = ['pdf', 'jpg', 'jpeg', 'png', 'gif', 'doc', 'docx'];
        $extension = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));

        if (!in_array($extension, $allowedExtensions)) {
            $errors[] = 'Ekstensi file tidak diizinkan';
        }

        return $errors;
    }

    /**
     * Generate descriptive filename based on document type
     */
    public function generateFileName($originalName, $siswaId, $jenisberkas) {
        $extension = pathinfo($originalName, PATHINFO_EXTENSION);
        $timestamp = date('YmdHis');
        $random = substr(md5(uniqid()), 0, 6);

        // Get descriptive name for document type
        $documentNames = $this->getDocumentNames();
        $docName = $documentNames[$jenisberkas] ?? $jenisberkas;

        // Clean document name for filename (remove spaces, special chars)
        $cleanDocName = $this->cleanForFilename($docName);

        return "{$cleanDocName}_Siswa_{$siswaId}_{$timestamp}_{$random}.{$extension}";
    }

    /**
     * Get descriptive document names for filename
     */
    public function getDocumentNames() {
        return [
            // Dokumen Identitas
            'kartu_keluarga' => 'Kartu_Keluarga',
            'akta_lahir' => 'Akta_Kelahiran',

            // Rapor
            'rapor_kelas_x' => 'Rapor_Kelas_X',
            'rapor_kelas_xi' => 'Rapor_Kelas_XI',
            'rapor_kelas_xii' => 'Rapor_Kelas_XII',

            // Ijazah
            'ijazah_sd' => 'Ijazah_SD',
            'ijazah_smp' => 'Ijazah_SMP',
            'ijazah_sma' => 'Ijazah_SMA',

            // Dokumen Pendukung
            'foto_siswa' => 'Foto_Siswa',
            'surat_keterangan_sehat' => 'Surat_Keterangan_Sehat',
            'surat_kelakuan_baik' => 'Surat_Kelakuan_Baik',
            'piagam_prestasi' => 'Piagam_Prestasi',
            'lainnya' => 'Dokumen_Lainnya'
        ];
    }

    /**
     * Clean string for use in filename
     */
    private function cleanForFilename($string) {
        // Remove or replace special characters
        $string = str_replace([' ', '-', '/', '\\', ':', '*', '?', '"', '<', '>', '|'], '_', $string);
        // Remove multiple underscores
        $string = preg_replace('/_+/', '_', $string);
        // Remove leading/trailing underscores
        $string = trim($string, '_');
        return $string;
    }

    /**
     * Get upload directory with subfolder based on document type
     */
    public function getUploadDir($jenisberkas = null) {
        $baseDir = __DIR__ . '/../../uploads/berkas/';

        if ($jenisberkas) {
            // Create subfolder based on document category
            $category = $this->getDocumentCategory($jenisberkas);
            $uploadDir = $baseDir . $category . '/';
        } else {
            $uploadDir = $baseDir;
        }

        if (!is_dir($uploadDir)) {
            mkdir($uploadDir, 0755, true);
        }
        return $uploadDir;
    }

    /**
     * Get document category for folder organization
     */
    public function getDocumentCategory($jenisberkas) {
        $categories = [
            // Dokumen Identitas
            'kartu_keluarga' => 'identitas',
            'akta_lahir' => 'identitas',

            // Rapor
            'rapor_kelas_x' => 'rapor',
            'rapor_kelas_xi' => 'rapor',
            'rapor_kelas_xii' => 'rapor',

            // Ijazah
            'ijazah_sd' => 'ijazah',
            'ijazah_smp' => 'ijazah',
            'ijazah_sma' => 'ijazah',

            // Dokumen Pendukung
            'foto_siswa' => 'foto',
            'surat_keterangan_sehat' => 'surat',
            'surat_kelakuan_baik' => 'surat',
            'piagam_prestasi' => 'prestasi',
            'lainnya' => 'lainnya'
        ];

        return $categories[$jenisberkas] ?? 'lainnya';
    }

    /**
     * Get full file path with category folder
     */
    public function getFullFilePath($jenisberkas, $filename) {
        $category = $this->getDocumentCategory($jenisberkas);
        return "uploads/berkas/{$category}/{$filename}";
    }
}
?>