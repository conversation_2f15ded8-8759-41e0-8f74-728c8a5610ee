import React from 'react';
import MainLayout from '../layouts/MainLayout';
import ApiTest from '../common/ApiTest';
import {
  SiswaStatus<PERSON>hart,
  SiswaPer<PERSON>elasChart,
  TrendPendaftaran<PERSON>hart,
  GenderDistributionChart,
  StatCard
} from '../charts/ChartComponents';
import { useDashboard, useRealTimeStats, useQuickActions } from '../../hooks/useDashboard';
import LoadingSpinner from '../common/LoadingSpinner';
import ErrorMessage from '../common/ErrorMessage';

const DashboardPage = () => {
  const { data, loading, error, lastUpdated, refresh } = useDashboard();
  const realTimeStats = useRealTimeStats();
  const { actions } = useQuickActions();

  if (loading) {
    return (
      <MainLayout>
        <LoadingSpinner text="Memuat dashboard..." />
      </MainLayout>
    );
  }

  if (error && !data) {
    return (
      <MainLayout>
        <ErrorMessage
          error={error}
          onRetry={refresh}
          title="Gagal Memuat Dashboard"
        />
      </MainLayout>
    );
  }

  return (
    <MainLayout>
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="mb-6 sm:mb-8">
          <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center mb-4">
            <h2 className="text-2xl sm:text-3xl font-bold text-gray-800 mb-2 sm:mb-0">Dashboard</h2>
            <div className="flex items-center space-x-4">
              <div className="text-sm text-gray-500">
                {lastUpdated && (
                  <>Terakhir diperbarui: {lastUpdated.toLocaleTimeString()}</>
                )}
              </div>
              <button
                onClick={refresh}
                className="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
              >
                <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                </svg>
                Refresh
              </button>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-md p-4 sm:p-6">
            <p className="text-base sm:text-lg text-gray-600 mb-2 sm:mb-4">
              Selamat datang di Sistem Informasi Akademik
            </p>
            <p className="text-sm sm:text-base text-gray-500">
              Kelola data siswa, riwayat kelas, dan berkas digital dengan mudah.
            </p>
          </div>
        </div>

        {/* Statistics Cards */}
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 sm:gap-6 mb-6 sm:mb-8">
          <StatCard
            title="Total Siswa"
            value={data?.overview?.total_siswa || 0}
            icon="👥"
            color="blue"
            subtitle="Semua status"
            trend={{ positive: true, value: "+5", period: "bulan ini" }}
          />
          <StatCard
            title="Siswa Aktif"
            value={data?.overview?.siswa_aktif || 0}
            icon="✅"
            color="green"
            subtitle="Sedang bersekolah"
          />
          <StatCard
            title="Lulus Tahun Ini"
            value={data?.overview?.siswa_lulus || 0}
            icon="🎓"
            color="purple"
            subtitle="Tahun 2024"
          />
          <StatCard
            title="Online Sekarang"
            value={realTimeStats.onlineUsers}
            icon="🟢"
            color="yellow"
            subtitle="User aktif"
          />
        </div>

        {/* Charts Section */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6 sm:mb-8">
          <SiswaStatusChart data={data?.siswaStatus} />
          <GenderDistributionChart data={data?.genderDistribution} />
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6 sm:mb-8">
          <SiswaPerKelasChart data={data?.siswaPerKelas} />
          <TrendPendaftaranChart data={data?.trendPendaftaran} />
        </div>

        {/* Quick Actions */}
        <div className="bg-white rounded-lg shadow-md p-4 sm:p-6 mb-6 sm:mb-8">
          <h3 className="text-lg font-semibold text-gray-800 mb-4">Quick Actions</h3>
          <div className="grid grid-cols-2 sm:grid-cols-4 gap-4">
            {actions.map((action) => (
              <button
                key={action.id}
                onClick={action.action}
                className="flex flex-col items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors"
              >
                <span className="text-2xl mb-2">{action.icon}</span>
                <span className="text-sm font-medium text-gray-700 text-center">{action.title}</span>
                <span className="text-xs text-gray-500 text-center mt-1">{action.description}</span>
              </button>
            ))}
          </div>
        </div>

        {/* Recent Activities */}
        <div className="bg-white rounded-lg shadow-md p-4 sm:p-6 mb-6 sm:mb-8">
          <h3 className="text-lg font-semibold text-gray-800 mb-4">Aktivitas Terbaru</h3>
          <div className="space-y-4">
            {data?.recentActivities?.map((activity) => (
              <div key={activity.id} className="flex items-start space-x-3 p-3 bg-gray-50 rounded-lg">
                <span className="text-xl">{activity.icon}</span>
                <div className="flex-1">
                  <p className="text-sm text-gray-800">{activity.message}</p>
                  <p className="text-xs text-gray-500 mt-1">{activity.timestamp}</p>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* API Test Component - Remove in production */}
        <div className="mt-6 sm:mt-8">
          <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-4">
            <p className="text-yellow-800 text-sm">
              <strong>Development Mode:</strong> API Test component ditampilkan untuk testing.
              Akan dihapus di production.
            </p>
          </div>
          <ApiTest />
        </div>
      </div>
    </MainLayout>
  );
};

export default DashboardPage;