import { useState, useEffect, useCallback } from 'react';
import api from '../services/api';
import { useToast } from '../contexts/ToastContext';

// Mock data untuk development (akan diganti dengan real API)
const mockDashboardData = {
  overview: {
    total_siswa: 150,
    siswa_aktif: 135,
    siswa_lulus: 12,
    siswa_mutasi: 2,
    siswa_dropout: 1,
    total_kelas: 9,
    total_guru: 25,
    total_berkas: 450
  },
  siswaStatus: {
    aktif: 135,
    lulus: 12,
    mutasi: 2,
    dropout: 1
  },
  genderDistribution: {
    L: 78,
    P: 72
  },
  siswaPerKelas: [
    { kelas: '10 IPA 1', jumlah: 32 },
    { kelas: '10 IPA 2', jumlah: 30 },
    { kelas: '10 IPS 1', jumlah: 28 },
    { kelas: '11 IPA 1', jumlah: 30 },
    { kelas: '11 IPA 2', jumlah: 32 },
    { kelas: '11 IPS 1', jumlah: 29 },
    { kelas: '12 IPA 1', jumlah: 28 },
    { kelas: '12 IPA 2', jumlah: 30 },
    { kelas: '12 IPS 1', jumlah: 27 }
  ],
  trendPendaftaran: [
    { bulan: 'Jan', jumlah: 5 },
    { bulan: 'Feb', jumlah: 8 },
    { bulan: 'Mar', jumlah: 12 },
    { bulan: 'Apr', jumlah: 15 },
    { bulan: 'May', jumlah: 18 },
    { bulan: 'Jun', jumlah: 22 },
    { bulan: 'Jul', jumlah: 25 },
    { bulan: 'Aug', jumlah: 20 },
    { bulan: 'Sep', jumlah: 15 },
    { bulan: 'Oct', jumlah: 10 },
    { bulan: 'Nov', jumlah: 8 },
    { bulan: 'Dec', jumlah: 2 }
  ],
  recentActivities: [
    {
      id: 1,
      type: 'siswa_baru',
      message: 'Ahmad Rizki Pratama telah terdaftar sebagai siswa baru',
      timestamp: '2 jam yang lalu',
      icon: '👤'
    },
    {
      id: 2,
      type: 'berkas_upload',
      message: 'Sari Indah Permata mengupload berkas ijazah',
      timestamp: '4 jam yang lalu',
      icon: '📁'
    },
    {
      id: 3,
      type: 'mutasi',
      message: 'Muhammad Fajar Sidiq mengajukan mutasi ke sekolah lain',
      timestamp: '1 hari yang lalu',
      icon: '🔄'
    },
    {
      id: 4,
      type: 'kelulusan',
      message: '30 siswa kelas 12 telah lulus',
      timestamp: '2 hari yang lalu',
      icon: '🎓'
    }
  ]
};

export const useDashboard = () => {
  const [data, setData] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [lastUpdated, setLastUpdated] = useState(null);
  const { showError } = useToast();

  const fetchDashboardData = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);

      // Try to fetch real data from API
      try {
        const response = await api.get('/dashboard/stats');
        setData(response.data.data);
      } catch (apiError) {
        // If API fails, use mock data
        console.warn('API not available, using mock data:', apiError.message);
        setData(mockDashboardData);
      }

      setLastUpdated(new Date());
    } catch (err) {
      console.error('Dashboard data error:', err);
      setError('Gagal memuat data dashboard');
      showError('Gagal memuat data dashboard');
      
      // Fallback to mock data
      setData(mockDashboardData);
    } finally {
      setLoading(false);
    }
  }, [showError]);

  useEffect(() => {
    fetchDashboardData();
  }, [fetchDashboardData]);

  // Auto refresh every 5 minutes
  useEffect(() => {
    const interval = setInterval(() => {
      fetchDashboardData();
    }, 5 * 60 * 1000); // 5 minutes

    return () => clearInterval(interval);
  }, [fetchDashboardData]);

  const refresh = useCallback(() => {
    fetchDashboardData();
  }, [fetchDashboardData]);

  return {
    data,
    loading,
    error,
    lastUpdated,
    refresh
  };
};

// Hook untuk statistik real-time
export const useRealTimeStats = () => {
  const [stats, setStats] = useState({
    onlineUsers: Math.floor(Math.random() * 10) + 1,
    todayRegistrations: Math.floor(Math.random() * 5),
    pendingApprovals: Math.floor(Math.random() * 8),
    systemStatus: 'online'
  });

  useEffect(() => {
    // Simulate real-time updates
    const interval = setInterval(() => {
      setStats(prev => ({
        ...prev,
        onlineUsers: Math.max(1, prev.onlineUsers + (Math.random() > 0.5 ? 1 : -1)),
        todayRegistrations: Math.max(0, prev.todayRegistrations + (Math.random() > 0.8 ? 1 : 0)),
        pendingApprovals: Math.max(0, prev.pendingApprovals + (Math.random() > 0.7 ? 1 : -1))
      }));
    }, 10000); // Update every 10 seconds

    return () => clearInterval(interval);
  }, []);

  return stats;
};

// Hook untuk quick actions
export const useQuickActions = () => {
  const { showSuccess, showError } = useToast();

  const actions = [
    {
      id: 'add_siswa',
      title: 'Tambah Siswa',
      description: 'Daftarkan siswa baru',
      icon: '👤',
      color: 'blue',
      action: () => {
        showSuccess('Fitur tambah siswa akan segera tersedia');
      }
    },
    {
      id: 'export_data',
      title: 'Export Data',
      description: 'Download laporan siswa',
      icon: '📊',
      color: 'green',
      action: () => {
        showSuccess('Fitur export data akan segera tersedia');
      }
    },
    {
      id: 'backup',
      title: 'Backup Data',
      description: 'Backup database',
      icon: '💾',
      color: 'purple',
      action: () => {
        showSuccess('Fitur backup akan segera tersedia');
      }
    },
    {
      id: 'settings',
      title: 'Pengaturan',
      description: 'Konfigurasi sistem',
      icon: '⚙️',
      color: 'gray',
      action: () => {
        showSuccess('Fitur pengaturan akan segera tersedia');
      }
    }
  ];

  return { actions };
};
