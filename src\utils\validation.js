// Input validation and sanitization utilities

/**
 * Sanitize string input to prevent XSS
 */
export const sanitizeString = (str) => {
  if (typeof str !== 'string') return '';
  
  return str
    .replace(/[<>]/g, '') // Remove < and > characters
    .replace(/javascript:/gi, '') // Remove javascript: protocol
    .replace(/on\w+=/gi, '') // Remove event handlers
    .trim();
};

/**
 * Validate email format
 */
export const isValidEmail = (email) => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
};

/**
 * Validate phone number (Indonesian format)
 */
export const isValidPhone = (phone) => {
  const phoneRegex = /^(\+62|62|0)[0-9]{9,13}$/;
  return phoneRegex.test(phone.replace(/[\s-]/g, ''));
};

/**
 * Validate NIS format
 */
export const isValidNIS = (nis) => {
  const nisRegex = /^[0-9]{6,10}$/;
  return nisRegex.test(nis);
};

/**
 * Validate NISN format
 */
export const isValidNISN = (nisn) => {
  const nisnRegex = /^[0-9]{10}$/;
  return nisnRegex.test(nisn);
};

/**
 * Validate password strength
 */
export const validatePassword = (password) => {
  const errors = [];
  
  if (password.length < 6) {
    errors.push('Password minimal 6 karakter');
  }
  
  if (!/[A-Za-z]/.test(password)) {
    errors.push('Password harus mengandung huruf');
  }
  
  if (!/[0-9]/.test(password)) {
    errors.push('Password harus mengandung angka');
  }
  
  return {
    isValid: errors.length === 0,
    errors
  };
};

/**
 * Validate file upload
 */
export const validateFile = (file, options = {}) => {
  const {
    maxSize = 5 * 1024 * 1024, // 5MB default
    allowedTypes = ['image/jpeg', 'image/png', 'application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document']
  } = options;

  const errors = [];

  if (!file) {
    errors.push('File harus dipilih');
    return { isValid: false, errors };
  }

  if (file.size > maxSize) {
    errors.push(`Ukuran file maksimal ${(maxSize / 1024 / 1024).toFixed(1)}MB`);
  }

  if (!allowedTypes.includes(file.type)) {
    errors.push('Tipe file tidak diizinkan');
  }

  return {
    isValid: errors.length === 0,
    errors
  };
};

/**
 * Validate form data
 */
export const validateSiswaForm = (data) => {
  const errors = {};

  // Required fields
  if (!data.nis || !data.nis.trim()) {
    errors.nis = 'NIS wajib diisi';
  } else if (!isValidNIS(data.nis)) {
    errors.nis = 'Format NIS tidak valid';
  }

  if (!data.nama_lengkap || !data.nama_lengkap.trim()) {
    errors.nama_lengkap = 'Nama lengkap wajib diisi';
  } else if (data.nama_lengkap.length < 2) {
    errors.nama_lengkap = 'Nama lengkap minimal 2 karakter';
  }

  if (!data.jenis_kelamin) {
    errors.jenis_kelamin = 'Jenis kelamin wajib dipilih';
  }

  // Optional but validated fields
  if (data.email && !isValidEmail(data.email)) {
    errors.email = 'Format email tidak valid';
  }

  if (data.no_telepon && !isValidPhone(data.no_telepon)) {
    errors.no_telepon = 'Format nomor telepon tidak valid';
  }

  if (data.nisn && !isValidNISN(data.nisn)) {
    errors.nisn = 'Format NISN tidak valid (10 digit)';
  }

  return {
    isValid: Object.keys(errors).length === 0,
    errors
  };
};

/**
 * Validate login form
 */
export const validateLoginForm = (data) => {
  const errors = {};

  if (!data.username || !data.username.trim()) {
    errors.username = 'Username wajib diisi';
  }

  if (!data.password || !data.password.trim()) {
    errors.password = 'Password wajib diisi';
  }

  return {
    isValid: Object.keys(errors).length === 0,
    errors
  };
};

/**
 * Sanitize form data
 */
export const sanitizeFormData = (data) => {
  const sanitized = {};
  
  for (const [key, value] of Object.entries(data)) {
    if (typeof value === 'string') {
      sanitized[key] = sanitizeString(value);
    } else {
      sanitized[key] = value;
    }
  }
  
  return sanitized;
};

/**
 * Escape HTML to prevent XSS
 */
export const escapeHtml = (text) => {
  const map = {
    '&': '&amp;',
    '<': '&lt;',
    '>': '&gt;',
    '"': '&quot;',
    "'": '&#039;'
  };
  
  return text.replace(/[&<>"']/g, (m) => map[m]);
};

/**
 * Validate search query
 */
export const validateSearchQuery = (query) => {
  if (!query || typeof query !== 'string') {
    return { isValid: false, error: 'Query pencarian tidak valid' };
  }

  if (query.length < 2) {
    return { isValid: false, error: 'Query pencarian minimal 2 karakter' };
  }

  if (query.length > 100) {
    return { isValid: false, error: 'Query pencarian maksimal 100 karakter' };
  }

  // Check for potential SQL injection patterns
  const sqlPatterns = /(\b(SELECT|INSERT|UPDATE|DELETE|DROP|CREATE|ALTER|EXEC|UNION|SCRIPT)\b)/i;
  if (sqlPatterns.test(query)) {
    return { isValid: false, error: 'Query pencarian mengandung karakter tidak diizinkan' };
  }

  return { isValid: true, sanitized: sanitizeString(query) };
};
