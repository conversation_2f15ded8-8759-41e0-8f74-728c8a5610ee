<h2><?= isset($siswa) ? 'Edit Student' : 'Add Student' ?></h2>
<form method="POST" action="<?= isset($siswa) ? '/siswa/edit/' . $siswa['id'] : '/siswa/create' ?>">
    <label>Name: <input type="text" name="nama" value="<?= isset($siswa) ? htmlspecialchars($siswa['nama']) : '' ?>" required></label><br>
    <label>Class: 
        <select name="kelas_id" required>
            <?php foreach ($kelas as $k): ?>
            <option value="<?= $k['id'] ?>" <?= isset($siswa) && $siswa['kelas_id'] == $k['id'] ? 'selected' : '' ?>>
                <?= htmlspecialchars($k['nama_kelas']) ?>
            </option>
            <?php endforeach; ?>
        </select>
    </label><br>
    <button type="submit">Save</button>
</form>