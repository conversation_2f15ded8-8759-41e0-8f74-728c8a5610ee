<?php

class SimpleSessionManager {
    private $sessionLifetime = 3600; // 1 hour
    
    public function __construct() {
        $this->configureSession();
        $this->startSession();
    }
    
    /**
     * Configure secure session settings
     */
    private function configureSession() {
        // Only configure if session is not active
        if (session_status() === PHP_SESSION_NONE) {
            // Set secure session parameters
            ini_set('session.cookie_httponly', 1);
            ini_set('session.cookie_secure', isset($_SERVER['HTTPS']));
            ini_set('session.use_strict_mode', 1);
            ini_set('session.cookie_samesite', 'Strict');
            
            // Set session lifetime
            ini_set('session.gc_maxlifetime', $this->sessionLifetime);
        }
    }
    
    /**
     * Start session
     */
    public function startSession() {
        if (session_status() === PHP_SESSION_NONE) {
            session_start();
            
            // Regenerate session ID periodically for security
            if (!isset($_SESSION['last_regeneration'])) {
                $this->regenerateSessionId();
            } elseif (time() - $_SESSION['last_regeneration'] > 300) { // 5 minutes
                $this->regenerateSessionId();
            }
        }
    }
    
    /**
     * Regenerate session ID
     */
    public function regenerateSessionId() {
        session_regenerate_id(true);
        $_SESSION['last_regeneration'] = time();
    }
    
    /**
     * Login user
     */
    public function loginUser($user) {
        $this->startSession();
        
        // Store user data in session
        $_SESSION['user_id'] = $user['id'];
        $_SESSION['username'] = $user['username'];
        $_SESSION['user_role'] = $user['role'];
        $_SESSION['nama_lengkap'] = $user['nama_lengkap'];
        $_SESSION['login_time'] = time();
        $_SESSION['last_activity'] = time();
        
        // Regenerate session ID for security
        $this->regenerateSessionId();
    }
    
    /**
     * Logout user
     */
    public function logoutUser() {
        $this->startSession();
        
        if (isset($_SESSION['user_id'])) {
            // Log security event
            Security::logSecurityEvent('user_logout', ['user_id' => $_SESSION['user_id']]);
        }
        
        // Clear session data
        $_SESSION = [];
        
        // Delete session cookie
        if (isset($_COOKIE[session_name()])) {
            setcookie(session_name(), '', time() - 3600, '/');
        }
        
        // Destroy session
        session_destroy();
    }
    
    /**
     * Check if session is valid
     */
    public function isValidSession() {
        $this->startSession();
        
        if (!isset($_SESSION['user_id'])) {
            return false;
        }
        
        // Check session timeout
        if (isset($_SESSION['last_activity']) && (time() - $_SESSION['last_activity']) > $this->sessionLifetime) {
            $this->logoutUser();
            return false;
        }
        
        // Update last activity
        $_SESSION['last_activity'] = time();
        
        return true;
    }
    
    /**
     * Get current user data
     */
    public function getCurrentUser() {
        $this->startSession();
        
        if (!$this->isValidSession()) {
            return null;
        }
        
        return [
            'id' => $_SESSION['user_id'],
            'username' => $_SESSION['username'],
            'role' => $_SESSION['user_role'],
            'nama_lengkap' => $_SESSION['nama_lengkap']
        ];
    }
}
