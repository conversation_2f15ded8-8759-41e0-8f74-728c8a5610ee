import React, { createContext, useContext, useState, useEffect } from 'react';

const ThemeContext = createContext();

export const useTheme = () => {
  const context = useContext(ThemeContext);
  if (!context) {
    throw new Error('useTheme must be used within a ThemeProvider');
  }
  return context;
};

// Available themes
const themes = {
  light: {
    name: 'Light',
    colors: {
      primary: '#3B82F6',
      secondary: '#6B7280',
      success: '#10B981',
      warning: '#F59E0B',
      error: '#EF4444',
      info: '#3B82F6',
      background: '#FFFFFF',
      surface: '#F9FAFB',
      text: '#111827',
      textSecondary: '#6B7280',
      border: '#E5E7EB'
    },
    shadows: {
      sm: '0 1px 2px 0 rgba(0, 0, 0, 0.05)',
      md: '0 4px 6px -1px rgba(0, 0, 0, 0.1)',
      lg: '0 10px 15px -3px rgba(0, 0, 0, 0.1)'
    }
  },
  dark: {
    name: 'Dark',
    colors: {
      primary: '#60A5FA',
      secondary: '#9CA3AF',
      success: '#34D399',
      warning: '#FBBF24',
      error: '#F87171',
      info: '#60A5FA',
      background: '#111827',
      surface: '#1F2937',
      text: '#F9FAFB',
      textSecondary: '#D1D5DB',
      border: '#374151'
    },
    shadows: {
      sm: '0 1px 2px 0 rgba(0, 0, 0, 0.3)',
      md: '0 4px 6px -1px rgba(0, 0, 0, 0.4)',
      lg: '0 10px 15px -3px rgba(0, 0, 0, 0.5)'
    }
  },
  blue: {
    name: 'Blue',
    colors: {
      primary: '#1E40AF',
      secondary: '#64748B',
      success: '#059669',
      warning: '#D97706',
      error: '#DC2626',
      info: '#1E40AF',
      background: '#F8FAFC',
      surface: '#F1F5F9',
      text: '#0F172A',
      textSecondary: '#475569',
      border: '#CBD5E1'
    },
    shadows: {
      sm: '0 1px 2px 0 rgba(30, 64, 175, 0.05)',
      md: '0 4px 6px -1px rgba(30, 64, 175, 0.1)',
      lg: '0 10px 15px -3px rgba(30, 64, 175, 0.1)'
    }
  },
  green: {
    name: 'Green',
    colors: {
      primary: '#059669',
      secondary: '#6B7280',
      success: '#10B981',
      warning: '#F59E0B',
      error: '#EF4444',
      info: '#3B82F6',
      background: '#F0FDF4',
      surface: '#ECFDF5',
      text: '#064E3B',
      textSecondary: '#6B7280',
      border: '#BBF7D0'
    },
    shadows: {
      sm: '0 1px 2px 0 rgba(5, 150, 105, 0.05)',
      md: '0 4px 6px -1px rgba(5, 150, 105, 0.1)',
      lg: '0 10px 15px -3px rgba(5, 150, 105, 0.1)'
    }
  }
};

export const ThemeProvider = ({ children }) => {
  const [currentTheme, setCurrentTheme] = useState('light');
  const [systemPreference, setSystemPreference] = useState('light');

  // Detect system theme preference
  useEffect(() => {
    const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
    setSystemPreference(mediaQuery.matches ? 'dark' : 'light');

    const handleChange = (e) => {
      setSystemPreference(e.matches ? 'dark' : 'light');
    };

    mediaQuery.addEventListener('change', handleChange);
    return () => mediaQuery.removeEventListener('change', handleChange);
  }, []);

  // Load saved theme from localStorage
  useEffect(() => {
    const savedTheme = localStorage.getItem('theme');
    if (savedTheme && themes[savedTheme]) {
      setCurrentTheme(savedTheme);
    } else if (savedTheme === 'system') {
      setCurrentTheme(systemPreference);
    }
  }, [systemPreference]);

  // Apply theme to document
  useEffect(() => {
    const theme = themes[currentTheme];
    if (theme) {
      const root = document.documentElement;
      
      // Apply CSS custom properties
      Object.entries(theme.colors).forEach(([key, value]) => {
        root.style.setProperty(`--color-${key}`, value);
      });

      Object.entries(theme.shadows).forEach(([key, value]) => {
        root.style.setProperty(`--shadow-${key}`, value);
      });

      // Apply dark class for Tailwind
      if (currentTheme === 'dark') {
        document.documentElement.classList.add('dark');
      } else {
        document.documentElement.classList.remove('dark');
      }
    }
  }, [currentTheme]);

  const changeTheme = (themeName) => {
    if (themeName === 'system') {
      setCurrentTheme(systemPreference);
      localStorage.setItem('theme', 'system');
    } else if (themes[themeName]) {
      setCurrentTheme(themeName);
      localStorage.setItem('theme', themeName);
    }
  };

  const getThemeColors = (themeName = currentTheme) => {
    return themes[themeName]?.colors || themes.light.colors;
  };

  const getThemeShadows = (themeName = currentTheme) => {
    return themes[themeName]?.shadows || themes.light.shadows;
  };

  const isDarkMode = () => {
    return currentTheme === 'dark';
  };

  const value = {
    currentTheme,
    themes,
    systemPreference,
    changeTheme,
    getThemeColors,
    getThemeShadows,
    isDarkMode
  };

  return (
    <ThemeContext.Provider value={value}>
      {children}
    </ThemeContext.Provider>
  );
};
