<?php
/**
 * Simple Login Test Page
 */

require_once 'app/config/db.php';
require_once 'app/models/User.php';
require_once 'app/helpers/Security.php';
require_once 'app/helpers/SimpleSessionManager.php';

$sessionManager = new SimpleSessionManager();
$userModel = new User();

// Handle login
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $username = $_POST['username'] ?? '';
    $password = $_POST['password'] ?? '';
    
    if (!empty($username) && !empty($password)) {
        $result = $userModel->authenticate($username, $password);
        
        if ($result['success']) {
            $sessionManager->loginUser($result['user']);
            echo "<div style='color: green; padding: 10px; background: #d4edda; border: 1px solid #c3e6cb; border-radius: 5px; margin: 10px 0;'>";
            echo "✅ Login successful! Welcome " . htmlspecialchars($result['user']['nama_lengkap']);
            echo "<br><a href='public/dashboard'>Go to Dashboard</a>";
            echo "</div>";
        } else {
            echo "<div style='color: red; padding: 10px; background: #f8d7da; border: 1px solid #f5c6cb; border-radius: 5px; margin: 10px 0;'>";
            echo "❌ " . htmlspecialchars($result['message']);
            echo "</div>";
        }
    }
}

// Check if already logged in
$currentUser = $sessionManager->getCurrentUser();
if ($currentUser) {
    echo "<div style='color: blue; padding: 10px; background: #d1ecf1; border: 1px solid #bee5eb; border-radius: 5px; margin: 10px 0;'>";
    echo "ℹ️ Already logged in as: " . htmlspecialchars($currentUser['nama_lengkap']);
    echo "<br><a href='public/dashboard'>Go to Dashboard</a> | <a href='?logout=1'>Logout</a>";
    echo "</div>";
}

// Handle logout
if (isset($_GET['logout'])) {
    $sessionManager->logoutUser();
    header('Location: simple_login.php');
    exit;
}
?>

<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Simple Login Test</title>
    <style>
        body { font-family: Arial, sans-serif; max-width: 500px; margin: 50px auto; padding: 20px; }
        .form-group { margin-bottom: 15px; }
        label { display: block; margin-bottom: 5px; font-weight: bold; }
        input[type="text"], input[type="password"] { width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px; }
        button { background: #007bff; color: white; padding: 10px 20px; border: none; border-radius: 4px; cursor: pointer; }
        button:hover { background: #0056b3; }
        .card { background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
    </style>
</head>
<body>
    <div class="card">
        <h2>🔐 Simple Login Test</h2>
        
        <form method="POST">
            <div class="form-group">
                <label for="username">Username:</label>
                <input type="text" id="username" name="username" value="admin" required>
            </div>
            
            <div class="form-group">
                <label for="password">Password:</label>
                <input type="password" id="password" name="password" value="password" required>
            </div>
            
            <button type="submit">Login</button>
        </form>
        
        <hr>
        <p><strong>Test Credentials:</strong></p>
        <ul>
            <li>Username: <code>admin</code></li>
            <li>Password: <code>password</code></li>
        </ul>
        
        <hr>
        <p><a href="public/">Go to Main Application</a></p>
        <p><a href="debug_routing.php">Debug Routing</a></p>
        <p><a href="test_db.php">Test Database</a></p>
    </div>
</body>
</html>
