<?php
/**
 * Database Import Script
 * Imports the enhanced schema into the database
 */

require_once 'app/config/db.php';

echo "<h2>Database Import Script</h2>";

try {
    $pdo = getDBConnection();
    echo "<p style='color: green;'>✅ Database connection successful!</p>";
    
    // Read SQL file
    $sqlFile = 'database/schema_enhanced.sql';
    if (!file_exists($sqlFile)) {
        throw new Exception("SQL file not found: $sqlFile");
    }
    
    $sql = file_get_contents($sqlFile);
    echo "<p>📄 Reading SQL file: $sqlFile</p>";
    
    // Split SQL into individual statements
    $statements = array_filter(
        array_map('trim', explode(';', $sql)),
        function($stmt) {
            return !empty($stmt) && !preg_match('/^\s*--/', $stmt);
        }
    );
    
    echo "<p>🔄 Executing " . count($statements) . " SQL statements...</p>";
    
    $pdo->beginTransaction();
    
    foreach ($statements as $statement) {
        if (trim($statement)) {
            try {
                $pdo->exec($statement);
                echo "<p style='color: green; font-size: 12px;'>✅ " . substr($statement, 0, 50) . "...</p>";
            } catch (PDOException $e) {
                // Skip errors for existing tables/data
                if (strpos($e->getMessage(), 'already exists') === false && 
                    strpos($e->getMessage(), 'Duplicate entry') === false) {
                    echo "<p style='color: orange; font-size: 12px;'>⚠️ " . $e->getMessage() . "</p>";
                }
            }
        }
    }
    
    $pdo->commit();
    
    echo "<p style='color: green; font-weight: bold;'>🎉 Database import completed successfully!</p>";
    
    // Verify admin user
    $stmt = $pdo->prepare("SELECT * FROM users WHERE username = 'admin'");
    $stmt->execute();
    if ($stmt->rowCount() > 0) {
        echo "<p style='color: green;'>✅ Admin user created successfully!</p>";
        echo "<p><strong>Login credentials:</strong></p>";
        echo "<ul>";
        echo "<li>Username: <code>admin</code></li>";
        echo "<li>Password: <code>password</code></li>";
        echo "</ul>";
    } else {
        echo "<p style='color: red;'>❌ Admin user not found after import!</p>";
    }
    
} catch (Exception $e) {
    if (isset($pdo)) {
        $pdo->rollback();
    }
    echo "<p style='color: red;'>❌ Import failed: " . $e->getMessage() . "</p>";
}

echo "<hr>";
echo "<p><a href='public/' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>🚀 Go to Application</a></p>";
echo "<p><a href='test_db.php'>🔍 Test Database Connection</a></p>";
?>
