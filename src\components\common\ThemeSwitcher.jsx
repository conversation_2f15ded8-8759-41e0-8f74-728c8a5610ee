import React, { useState } from 'react';
import { useTheme } from '../../contexts/ThemeContext';
import { useTranslation } from 'react-i18next';

const ThemeSwitcher = ({ className = "" }) => {
  const { currentTheme, themes, changeTheme } = useTheme();
  const { t } = useTranslation();
  const [isOpen, setIsOpen] = useState(false);

  const themeOptions = [
    {
      key: 'light',
      name: t('settings.lightTheme'),
      icon: '☀️',
      colors: ['#3B82F6', '#10B981', '#F59E0B']
    },
    {
      key: 'dark',
      name: t('settings.darkTheme'),
      icon: '🌙',
      colors: ['#60A5FA', '#34D399', '#FBBF24']
    },
    {
      key: 'blue',
      name: 'Blue',
      icon: '🔵',
      colors: ['#1E40AF', '#059669', '#D97706']
    },
    {
      key: 'green',
      name: 'Green',
      icon: '🟢',
      colors: ['#059669', '#10B981', '#F59E0B']
    },
    {
      key: 'system',
      name: t('settings.systemTheme'),
      icon: '💻',
      colors: ['#6B7280', '#9CA3AF', '#D1D5DB']
    }
  ];

  const currentThemeOption = themeOptions.find(option => option.key === currentTheme) || themeOptions[0];

  const handleThemeChange = (themeKey) => {
    changeTheme(themeKey);
    setIsOpen(false);
  };

  return (
    <div className={`relative ${className}`}>
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="flex items-center space-x-2 px-3 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 dark:bg-gray-800 dark:text-gray-200 dark:border-gray-600 dark:hover:bg-gray-700"
      >
        <span className="text-lg">{currentThemeOption.icon}</span>
        <span className="hidden sm:inline">{currentThemeOption.name}</span>
        <svg
          className={`w-4 h-4 transition-transform ${isOpen ? 'rotate-180' : ''}`}
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
        >
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
        </svg>
      </button>

      {isOpen && (
        <>
          {/* Backdrop */}
          <div
            className="fixed inset-0 z-10"
            onClick={() => setIsOpen(false)}
          />
          
          {/* Dropdown */}
          <div className="absolute right-0 mt-2 w-64 bg-white rounded-md shadow-lg ring-1 ring-black ring-opacity-5 z-20 dark:bg-gray-800 dark:ring-gray-600">
            <div className="py-2">
              <div className="px-4 py-2 text-xs font-medium text-gray-500 uppercase tracking-wider dark:text-gray-400">
                {t('settings.selectTheme')}
              </div>
              {themeOptions.map((option) => (
                <button
                  key={option.key}
                  onClick={() => handleThemeChange(option.key)}
                  className={`flex items-center w-full px-4 py-3 text-sm text-left hover:bg-gray-100 dark:hover:bg-gray-700 ${
                    currentTheme === option.key
                      ? 'bg-blue-50 text-blue-700 dark:bg-blue-900 dark:text-blue-200'
                      : 'text-gray-700 dark:text-gray-200'
                  }`}
                >
                  <span className="text-lg mr-3">{option.icon}</span>
                  <div className="flex-1">
                    <div className="font-medium">{option.name}</div>
                    <div className="flex items-center mt-1 space-x-1">
                      {option.colors.map((color, index) => (
                        <div
                          key={index}
                          className="w-3 h-3 rounded-full border border-gray-300 dark:border-gray-600"
                          style={{ backgroundColor: color }}
                        />
                      ))}
                    </div>
                  </div>
                  {currentTheme === option.key && (
                    <svg className="w-4 h-4 ml-2 text-blue-600 dark:text-blue-400" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                    </svg>
                  )}
                </button>
              ))}
            </div>
          </div>
        </>
      )}
    </div>
  );
};

export default ThemeSwitcher;
