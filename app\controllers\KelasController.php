<?php
require_once __DIR__ . '/../models/Kelas.php';

class KelasController {
    private $kelas;

    public function __construct() {
        $this->kelas = new Kelas();
    }

    public function index() {
        $data['kelas'] = $this->kelas->getAll();
        $this->view('kelas/list', $data);
    }

    public function create() {
        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            $nama_kelas = $_POST['nama_kelas'] ?? '';
            if ($nama_kelas) {
                $this->kelas->create($nama_kelas);
                header('Location: /kelas');
                exit;
            }
        }
        $this->view('kelas/form');
    }

    private function view($path, $data = []) {
        extract($data);
        require_once __DIR__ . '/../views/layouts/header.php';
        require_once __DIR__ . '/../views/' . $path . '.php';
        require_once __DIR__ . '/../views/layouts/footer.php';
    }
}
?>