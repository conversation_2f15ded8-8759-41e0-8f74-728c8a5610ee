{"common": {"loading": "Loading...", "error": "Error occurred", "success": "Success", "warning": "Warning", "info": "Information", "save": "Save", "cancel": "Cancel", "delete": "Delete", "edit": "Edit", "add": "Add", "search": "Search", "filter": "Filter", "reset": "Reset", "refresh": "Refresh", "download": "Download", "upload": "Upload", "close": "Close", "back": "Back", "next": "Next", "previous": "Previous", "yes": "Yes", "no": "No", "confirm": "Confirm", "required": "Required", "optional": "Optional"}, "navigation": {"dashboard": "Dashboard", "students": "Students", "classes": "Classes", "reports": "Reports", "settings": "Settings", "logout": "Logout"}, "auth": {"login": "<PERSON><PERSON>", "logout": "Logout", "username": "Username", "password": "Password", "loginTitle": "Sign In to System", "loginSubtitle": "Enter your credentials to access the system", "loginButton": "Sign In", "loginSuccess": "Login successful! Welcome.", "loginError": "Invalid username or password", "sessionExpired": "Your session has expired. Please login again.", "unauthorized": "You don't have permission to access this page"}, "dashboard": {"title": "Dashboard", "welcome": "Welcome to Academic Information System", "subtitle": "Manage student data, class history, and digital documents easily", "lastUpdated": "Last updated", "totalStudents": "Total Students", "activeStudents": "Active Students", "graduatedThisYear": "Graduated This Year", "onlineNow": "Online Now", "quickActions": "Quick Actions", "recentActivities": "Recent Activities", "studentStatus": "Student Status", "genderDistribution": "Gender Distribution", "studentsPerClass": "Students per Class Distribution", "registrationTrend": "Student Registration Trend (2024)"}, "students": {"title": "Student List", "addStudent": "Add Student", "searchPlaceholder": "Search by name or NIS...", "status": "Status", "class": "Class", "allStatus": "All Status", "allClasses": "All Classes", "active": "Active", "graduated": "Graduated", "transferred": "Transferred", "dropout": "Dropout", "resetFilter": "Reset Filter", "showingResults": "Showing {{count}} of {{total}} students", "noResults": "No students found", "searchFor": "for search \"{{query}}\"", "detail": "Detail", "uploadFiles": "Upload Files", "noStudents": "No student data yet", "addFirstStudent": "Add the first student to get started", "page": "Page", "of": "of", "totalStudentsCount": "{{count}} total students"}, "studentDetail": {"title": "Student Detail", "personalInfo": "Personal Information", "academicInfo": "Academic Information", "contactInfo": "Contact Information", "documents": "Documents", "classHistory": "Class History", "fullName": "Full Name", "nis": "NIS", "nisn": "NISN", "gender": "Gender", "birthPlace": "Birth Place", "birthDate": "Birth Date", "religion": "Religion", "address": "Address", "phone": "Phone Number", "email": "Email", "parentName": "Parent Name", "parentPhone": "Parent Phone", "initialClass": "Initial Class", "currentClass": "Current Class", "status": "Status", "enrollmentDate": "Enrollment Date", "graduationDate": "Graduation Date", "male": "Male", "female": "Female"}, "fileUpload": {"title": "Upload Files", "dragDrop": "Drag & drop files or", "selectFile": "Select Files", "maxSize": "Maximum {{size}} per file", "maxFiles": "Maximum {{count}} files", "formats": "Formats", "selectedFiles": "Selected files", "uploading": "Uploading files...", "uploadSuccess": "File {{filename}} selected successfully", "uploadError": "Invalid file", "removeFile": "Remove file", "fileTypes": {"rapor": "Report Card", "ijazah": "Certificate", "kartu_keluarga": "Family Card", "akta_lahir": "Birth Certificate", "foto": "Photo"}}, "notifications": {"title": "Notifications", "markAllRead": "Mark all as read", "deleteAll": "Delete All", "viewAll": "View All", "noNotifications": "No notifications", "newNotification": "You have new notifications", "deleteNotification": "Delete notification", "justNow": "Just now", "minutesAgo": "{{count}} minutes ago", "hoursAgo": "{{count}} hours ago", "daysAgo": "{{count}} days ago"}, "errors": {"networkError": "Cannot connect to server. Please check your internet connection.", "validationError": "The submitted data is invalid.", "unauthorized": "Your session has expired. Please login again.", "forbidden": "You don't have permission to perform this action.", "notFound": "The requested data was not found.", "conflict": "Data already exists or conflict occurred.", "serverError": "Server error occurred. Please try again later.", "serviceUnavailable": "Server is currently unavailable. Please try again later.", "unknownError": "An unknown error occurred.", "tryAgain": "Try Again", "backToDashboard": "Back to Dashboard"}, "validation": {"required": "{{field}} is required", "minLength": "{{field}} minimum {{count}} characters", "maxLength": "{{field}} maximum {{count}} characters", "invalidEmail": "Invalid email format", "invalidPhone": "Invalid phone number format", "invalidNIS": "Invalid NIS format", "invalidNISN": "Invalid NISN format (10 digits)", "passwordTooShort": "Password minimum 6 characters", "passwordNoLetter": "Password must contain letters", "passwordNoNumber": "Password must contain numbers", "fileTooLarge": "File size maximum {{size}}", "invalidFileType": "File type not allowed", "searchTooShort": "Search query minimum 2 characters", "searchTooLong": "Search query maximum 100 characters", "invalidCharacters": "Search query contains invalid characters"}, "settings": {"title": "Settings", "language": "Language", "theme": "Theme", "notifications": "Notifications", "account": "Account", "system": "System", "selectLanguage": "Select Language", "selectTheme": "Select Theme", "lightTheme": "Light", "darkTheme": "Dark", "systemTheme": "System", "emailNotifications": "Email Notifications", "pushNotifications": "Push Notifications", "smsNotifications": "SMS Notifications", "notificationTypes": "Notification Types", "newStudent": "New Student", "fileUpload": "File Upload", "transfer": "Transfer", "graduation": "Graduation", "systemUpdates": "System Updates", "saveSettings": "Save Settings", "settingsSaved": "Setting<PERSON> saved successfully"}}