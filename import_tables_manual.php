<?php
/**
 * Manual Table Import Script
 * Import tabel satu per satu untuk menghindari error
 */

require_once 'app/config/db.php';

echo "<h2>🔧 Manual Table Import</h2>";

try {
    $pdo = getDBConnection();
    echo "<p style='color: green;'>✅ Database connection successful!</p>";
    
    // Array of SQL statements to execute one by one
    $sqlStatements = [
        // Create kelas table
        "CREATE TABLE IF NOT EXISTS kelas (
            id INT PRIMARY KEY AUTO_INCREMENT,
            nama_kelas VARCHAR(20) NOT NULL,
            tingkat INT NOT NULL,
            jurusan <PERSON>RCHAR(50),
            tahun_pelajaran VARCHAR(9) NOT NULL,
            wali_kelas VARCHAR(100),
            kapasitas INT DEFAULT 30,
            is_active BOOLEAN DEFAULT TRUE,
            created_by INT,
            updated_by INT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            
            FOREIGN KEY (created_by) REFERENCES users(id),
            FOREIGN KEY (updated_by) REFERENCES users(id),
            INDEX idx_tingkat (tingkat),
            INDEX idx_tahun (tahun_pelajaran),
            INDEX idx_active (is_active)
        )",
        
        // Create siswa table
        "CREATE TABLE IF NOT EXISTS siswa (
            id_siswa INT PRIMARY KEY AUTO_INCREMENT,
            nis VARCHAR(20) UNIQUE NOT NULL,
            nisn VARCHAR(20) UNIQUE,
            nama_lengkap VARCHAR(100) NOT NULL,
            jenis_kelamin ENUM('L', 'P') NOT NULL,
            tempat_lahir VARCHAR(50),
            tanggal_lahir DATE,
            alamat TEXT,
            no_telepon VARCHAR(20),
            email VARCHAR(100),
            nama_ayah VARCHAR(100),
            nama_ibu VARCHAR(100),
            pekerjaan_ayah VARCHAR(50),
            pekerjaan_ibu VARCHAR(50),
            kelas_id INT,
            tahun_masuk YEAR,
            status_siswa ENUM('aktif', 'lulus', 'mutasi', 'dropout') DEFAULT 'aktif',
            foto VARCHAR(255),
            created_by INT,
            updated_by INT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            
            FOREIGN KEY (kelas_id) REFERENCES kelas(id),
            FOREIGN KEY (created_by) REFERENCES users(id),
            FOREIGN KEY (updated_by) REFERENCES users(id),
            INDEX idx_nis (nis),
            INDEX idx_nama (nama_lengkap),
            INDEX idx_status (status_siswa),
            INDEX idx_kelas (kelas_id)
        )",
        
        // Create berkas table
        "CREATE TABLE IF NOT EXISTS berkas (
            id INT PRIMARY KEY AUTO_INCREMENT,
            siswa_id INT NOT NULL,
            jenis_berkas ENUM('kartu_keluarga', 'akta_lahir', 'rapor', 'ijazah_sebelumnya', 'foto', 'surat_keterangan_sehat', 'lainnya') NOT NULL,
            nama_berkas VARCHAR(255) NOT NULL,
            nama_file_asli VARCHAR(255) NOT NULL,
            nama_file_sistem VARCHAR(255) NOT NULL,
            ukuran_file INT NOT NULL,
            mime_type VARCHAR(100) NOT NULL,
            file_path VARCHAR(500) NOT NULL,
            file_hash VARCHAR(64) NOT NULL,
            keterangan TEXT,
            uploaded_by INT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            
            FOREIGN KEY (siswa_id) REFERENCES siswa(id_siswa) ON DELETE CASCADE,
            FOREIGN KEY (uploaded_by) REFERENCES users(id),
            INDEX idx_siswa (siswa_id),
            INDEX idx_jenis (jenis_berkas),
            INDEX idx_hash (file_hash)
        )",
        
        // Create audit_logs table
        "CREATE TABLE IF NOT EXISTS audit_logs (
            id INT PRIMARY KEY AUTO_INCREMENT,
            user_id INT,
            action VARCHAR(50) NOT NULL,
            table_name VARCHAR(50) NOT NULL,
            record_id INT,
            old_values JSON,
            new_values JSON,
            ip_address VARCHAR(45),
            user_agent TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            
            FOREIGN KEY (user_id) REFERENCES users(id),
            INDEX idx_user (user_id),
            INDEX idx_action (action),
            INDEX idx_table (table_name),
            INDEX idx_created (created_at)
        )"
    ];
    
    // Sample data inserts
    $sampleData = [
        "INSERT IGNORE INTO kelas (nama_kelas, tingkat, jurusan, tahun_pelajaran, wali_kelas, created_by) VALUES 
         ('X-IPA-1', 10, 'IPA', '2024/2025', 'Budi Santoso', 1)",
        
        "INSERT IGNORE INTO kelas (nama_kelas, tingkat, jurusan, tahun_pelajaran, wali_kelas, created_by) VALUES 
         ('X-IPS-1', 10, 'IPS', '2024/2025', 'Siti Aminah', 1)",
        
        "INSERT IGNORE INTO kelas (nama_kelas, tingkat, jurusan, tahun_pelajaran, wali_kelas, created_by) VALUES 
         ('XI-IPA-1', 11, 'IPA', '2024/2025', 'Ahmad Rahman', 1)",
        
        "INSERT IGNORE INTO siswa (nis, nisn, nama_lengkap, jenis_kelamin, tempat_lahir, tanggal_lahir, alamat, kelas_id, tahun_masuk, created_by) VALUES 
         ('2024001', '1234567890', 'Ahmad Fauzi', 'L', 'Jakarta', '2007-05-15', 'Jl. Merdeka No. 123, Jakarta', 1, 2024, 1)",
        
        "INSERT IGNORE INTO siswa (nis, nisn, nama_lengkap, jenis_kelamin, tempat_lahir, tanggal_lahir, alamat, kelas_id, tahun_masuk, created_by) VALUES 
         ('2024002', '1234567891', 'Siti Nurhaliza', 'P', 'Bandung', '2007-08-20', 'Jl. Sudirman No. 456, Bandung', 1, 2024, 1)"
    ];
    
    echo "<h3>🏗️ Creating Tables...</h3>";
    
    // Execute table creation statements
    foreach ($sqlStatements as $index => $sql) {
        try {
            $pdo->exec($sql);
            $tableNames = ['kelas', 'siswa', 'berkas', 'audit_logs'];
            echo "<p style='color: green;'>✅ Table created: <strong>{$tableNames[$index]}</strong></p>";
        } catch (PDOException $e) {
            if (strpos($e->getMessage(), 'already exists') !== false) {
                echo "<p style='color: orange;'>⚠️ Table already exists: {$tableNames[$index]}</p>";
            } else {
                echo "<p style='color: red;'>❌ Error creating table: " . htmlspecialchars($e->getMessage()) . "</p>";
            }
        }
    }
    
    echo "<h3>📝 Inserting Sample Data...</h3>";
    
    // Execute sample data inserts
    foreach ($sampleData as $sql) {
        try {
            $pdo->exec($sql);
            echo "<p style='color: green; font-size: 12px;'>✅ Sample data inserted</p>";
        } catch (PDOException $e) {
            if (strpos($e->getMessage(), 'Duplicate entry') !== false) {
                echo "<p style='color: orange; font-size: 12px;'>⚠️ Sample data already exists</p>";
            } else {
                echo "<p style='color: red; font-size: 12px;'>❌ Error inserting data: " . htmlspecialchars($e->getMessage()) . "</p>";
            }
        }
    }
    
    echo "<h3>📊 Verification</h3>";
    
    // Simple table check
    $tables = ['users', 'sessions', 'csrf_tokens', 'kelas', 'siswa', 'berkas', 'audit_logs'];
    
    echo "<ul>";
    foreach ($tables as $table) {
        try {
            $count = $pdo->query("SELECT COUNT(*) FROM `$table`")->fetchColumn();
            echo "<li style='color: green;'>✅ <strong>$table</strong>: $count records</li>";
        } catch (Exception $e) {
            echo "<li style='color: red;'>❌ <strong>$table</strong>: Error - " . htmlspecialchars($e->getMessage()) . "</li>";
        }
    }
    echo "</ul>";
    
    echo "<p style='color: green; font-weight: bold; font-size: 18px;'>🎉 Database setup completed successfully!</p>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Setup failed: " . htmlspecialchars($e->getMessage()) . "</p>";
}

echo "<hr>";
echo "<p><a href='public/' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>🚀 Go to Application</a></p>";
echo "<p><a href='simple_dashboard.php'>📊 Simple Dashboard</a></p>";
echo "<p><a href='test_db.php'>🔍 Test Database</a></p>";
?>
