<?php
/**
 * Complete Database Setup Script
 * Menambahkan tabel yang kurang dan data sample
 */

require_once 'app/config/db.php';

echo "<h2>🔧 Complete Database Setup</h2>";

try {
    $pdo = getDBConnection();
    echo "<p style='color: green;'>✅ Database connection successful!</p>";
    
    // Read SQL file
    $sqlFile = 'database/complete_missing_tables.sql';
    if (!file_exists($sqlFile)) {
        throw new Exception("SQL file not found: $sqlFile");
    }
    
    $sql = file_get_contents($sqlFile);
    echo "<p>📄 Reading SQL file: $sqlFile</p>";
    
    // Split SQL into individual statements
    $statements = array_filter(
        array_map('trim', explode(';', $sql)),
        function($stmt) {
            return !empty($stmt) && 
                   !preg_match('/^\s*--/', $stmt) && 
                   !preg_match('/^\s*\/\*/', $stmt) &&
                   !preg_match('/^\s*USE\s+/', $stmt);
        }
    );
    
    echo "<p>🔄 Executing " . count($statements) . " SQL statements...</p>";
    
    $pdo->beginTransaction();
    
    $successCount = 0;
    $skipCount = 0;
    
    foreach ($statements as $statement) {
        if (trim($statement)) {
            try {
                $pdo->exec($statement);
                $successCount++;
                
                // Show only important operations
                if (strpos($statement, 'CREATE TABLE') !== false) {
                    $tableName = '';
                    if (preg_match('/CREATE TABLE.*?`?(\w+)`?\s*\(/i', $statement, $matches)) {
                        $tableName = $matches[1];
                    }
                    echo "<p style='color: green; font-size: 14px;'>✅ Table created: <strong>$tableName</strong></p>";
                } elseif (strpos($statement, 'INSERT') !== false) {
                    echo "<p style='color: blue; font-size: 12px;'>📝 Sample data inserted</p>";
                }
                
            } catch (PDOException $e) {
                // Skip errors for existing tables/data
                if (strpos($e->getMessage(), 'already exists') !== false || 
                    strpos($e->getMessage(), 'Duplicate entry') !== false) {
                    $skipCount++;
                    echo "<p style='color: orange; font-size: 12px;'>⚠️ Skipped (already exists)</p>";
                } else {
                    echo "<p style='color: red; font-size: 12px;'>❌ Error: " . $e->getMessage() . "</p>";
                }
            }
        }
    }
    
    $pdo->commit();
    
    echo "<p style='color: green; font-weight: bold;'>🎉 Database setup completed!</p>";
    echo "<p>📊 <strong>Summary:</strong> $successCount successful, $skipCount skipped</p>";
    
    // Verify tables
    echo "<h3>📋 Database Tables Status</h3>";
    $tables = $pdo->query("SHOW TABLES")->fetchAll(PDO::FETCH_COLUMN);
    
    $expectedTables = ['users', 'sessions', 'csrf_tokens', 'kelas', 'siswa', 'berkas', 'audit_logs'];
    
    echo "<table border='1' cellpadding='10' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr style='background: #f8f9fa;'><th>Table Name</th><th>Status</th><th>Records</th></tr>";
    
    foreach ($expectedTables as $table) {
        $exists = in_array($table, $tables);
        $status = $exists ? "✅ Exists" : "❌ Missing";
        $color = $exists ? "green" : "red";
        
        $recordCount = 0;
        if ($exists) {
            try {
                $count = $pdo->query("SELECT COUNT(*) FROM $table")->fetchColumn();
                $recordCount = $count;
            } catch (Exception $e) {
                $recordCount = "Error";
            }
        }
        
        echo "<tr>";
        echo "<td><strong>$table</strong></td>";
        echo "<td style='color: $color;'>$status</td>";
        echo "<td>$recordCount</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    // Show sample data
    echo "<h3>👥 Sample Data</h3>";
    
    if (in_array('kelas', $tables)) {
        $kelas = $pdo->query("SELECT nama_kelas, tingkat, jurusan FROM kelas LIMIT 3")->fetchAll();
        echo "<p><strong>Sample Classes:</strong></p>";
        echo "<ul>";
        foreach ($kelas as $k) {
            echo "<li>{$k['nama_kelas']} - Tingkat {$k['tingkat']} {$k['jurusan']}</li>";
        }
        echo "</ul>";
    }
    
    if (in_array('siswa', $tables)) {
        $siswa = $pdo->query("SELECT nama_lengkap, nis, jenis_kelamin FROM siswa LIMIT 3")->fetchAll();
        echo "<p><strong>Sample Students:</strong></p>";
        echo "<ul>";
        foreach ($siswa as $s) {
            $gender = $s['jenis_kelamin'] == 'L' ? 'Laki-laki' : 'Perempuan';
            echo "<li>{$s['nama_lengkap']} (NIS: {$s['nis']}) - $gender</li>";
        }
        echo "</ul>";
    }
    
    echo "<p style='color: green; font-weight: bold; font-size: 18px;'>🎉 Database lengkap dan siap digunakan!</p>";
    
} catch (Exception $e) {
    if (isset($pdo)) {
        $pdo->rollback();
    }
    echo "<p style='color: red;'>❌ Setup failed: " . htmlspecialchars($e->getMessage()) . "</p>";
}

echo "<hr>";
echo "<p><a href='public/' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>🚀 Go to Application</a></p>";
echo "<p><a href='simple_dashboard.php'>📊 Simple Dashboard</a></p>";
echo "<p><a href='test_db.php'>🔍 Test Database</a></p>";
?>
