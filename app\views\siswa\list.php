<div class="container-fluid">
    <!-- <PERSON> Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="h3 mb-0 text-gray-800">
                        <i class="bi bi-people text-primary"></i>
                        Data Siswa
                    </h1>
                    <p class="text-muted mb-0">Kelola data siswa sekolah</p>
                </div>
                <div>
                    <a href="/siswa-app/public/siswa/create" class="btn btn-primary">
                        <i class="bi bi-person-plus"></i>
                        Tambah Siswa
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Data Table -->
    <div class="row">
        <div class="col-12">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="bi bi-table"></i>
                        Daftar Siswa
                    </h6>
                </div>
                <div class="card-body">
                    <?php if (isset($siswa) && is_array($siswa) && count($siswa) > 0): ?>
                        <div class="table-responsive">
                            <table class="table table-bordered table-hover">
                                <thead class="table-primary">
                                    <tr>
                                        <th width="80">NIS</th>
                                        <th>Nama Lengkap</th>
                                        <th width="100">Jenis Kelamin</th>
                                        <th width="150">Kelas</th>
                                        <th width="100">Status</th>
                                        <th width="200">Aksi</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($siswa as $s): ?>
                                    <tr>
                                        <td>
                                            <strong><?= htmlspecialchars($s['nis'] ?? 'N/A') ?></strong>
                                        </td>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <div class="avatar-sm me-2">
                                                    <i class="bi bi-person-circle text-muted" style="font-size: 1.5rem;"></i>
                                                </div>
                                                <div>
                                                    <strong><?= htmlspecialchars($s['nama_lengkap'] ?? $s['nama'] ?? 'N/A') ?></strong>
                                                    <?php if (!empty($s['email'])): ?>
                                                        <br><small class="text-muted"><?= htmlspecialchars($s['email']) ?></small>
                                                    <?php endif; ?>
                                                </div>
                                            </div>
                                        </td>
                                        <td>
                                            <?php
                                            $gender = $s['jenis_kelamin'] ?? 'L';
                                            $genderText = $gender === 'L' ? 'Laki-laki' : 'Perempuan';
                                            $genderIcon = $gender === 'L' ? 'bi-gender-male text-primary' : 'bi-gender-female text-danger';
                                            ?>
                                            <i class="bi <?= $genderIcon ?>"></i>
                                            <?= $genderText ?>
                                        </td>
                                        <td>
                                            <?php if (!empty($s['nama_kelas'])): ?>
                                                <span class="badge bg-info">
                                                    <?= htmlspecialchars($s['nama_kelas']) ?>
                                                </span>
                                            <?php else: ?>
                                                <span class="badge bg-secondary">Belum Ada Kelas</span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <?php
                                            $status = $s['status_siswa'] ?? 'aktif';
                                            $statusClass = [
                                                'aktif' => 'bg-success',
                                                'lulus' => 'bg-primary',
                                                'mutasi' => 'bg-warning',
                                                'dropout' => 'bg-danger'
                                            ];
                                            ?>
                                            <span class="badge <?= $statusClass[$status] ?? 'bg-secondary' ?>">
                                                <?= ucfirst($status) ?>
                                            </span>
                                        </td>
                                        <td>
                                            <div class="btn-group" role="group">
                                                <a href="/siswa-app/public/siswa/detail/<?= $s['id_siswa'] ?? $s['id'] ?>"
                                                   class="btn btn-sm btn-outline-info" title="Lihat Detail">
                                                    <i class="bi bi-eye"></i>
                                                </a>
                                                <a href="/siswa-app/public/siswa/edit/<?= $s['id_siswa'] ?? $s['id'] ?>"
                                                   class="btn btn-sm btn-outline-warning" title="Edit">
                                                    <i class="bi bi-pencil"></i>
                                                </a>
                                                <a href="/siswa-app/public/siswa/delete/<?= $s['id_siswa'] ?? $s['id'] ?>"
                                                   class="btn btn-sm btn-outline-danger" title="Hapus"
                                                   onclick="return confirm('Yakin ingin menghapus siswa ini?')">
                                                    <i class="bi bi-trash"></i>
                                                </a>
                                            </div>
                                        </td>
                                    </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php else: ?>
                        <div class="text-center py-5">
                            <i class="bi bi-people" style="font-size: 4rem; color: #dee2e6;"></i>
                            <h4 class="text-muted mt-3">Belum Ada Data Siswa</h4>
                            <p class="text-muted">Silakan tambahkan siswa baru untuk memulai.</p>
                            <a href="/siswa-app/public/siswa/create" class="btn btn-primary">
                                <i class="bi bi-person-plus"></i>
                                Tambah Siswa Pertama
                            </a>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>