<?php
/**
 * Simple Dashboard Test
 */

error_reporting(E_ALL);
ini_set('display_errors', 1);

require_once 'app/helpers/Security.php';
require_once 'app/helpers/SimpleSessionManager.php';

$sessionManager = new SimpleSessionManager();

// Check authentication
if (!Security::isAuthenticated()) {
    echo "<p style='color: red;'>❌ Not authenticated. <a href='simple_login.php'>Please login first</a></p>";
    exit;
}

$currentUser = $sessionManager->getCurrentUser();

?>
<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Simple Dashboard</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
</head>
<body class="bg-light">
    
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container-fluid">
            <a class="navbar-brand" href="#">
                <i class="bi bi-mortarboard"></i> SISWA APP
            </a>
            
            <div class="navbar-nav ms-auto">
                <div class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                        <i class="bi bi-person-circle"></i>
                        <?= htmlspecialchars($currentUser['nama_lengkap']) ?>
                    </a>
                    <ul class="dropdown-menu">
                        <li><a class="dropdown-item" href="simple_login.php?logout=1">
                            <i class="bi bi-box-arrow-right"></i> Logout
                        </a></li>
                    </ul>
                </div>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="container-fluid mt-4">
        <div class="row">
            <div class="col-12">
                <h1 class="h3 mb-4">
                    <i class="bi bi-speedometer2 text-primary"></i>
                    Dashboard
                </h1>
            </div>
        </div>

        <!-- Welcome Message -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="alert alert-success">
                    <h4 class="alert-heading">🎉 Selamat Datang!</h4>
                    <p>Halo <strong><?= htmlspecialchars($currentUser['nama_lengkap']) ?></strong>, 
                       Anda berhasil login sebagai <strong><?= ucfirst($currentUser['role']) ?></strong>.</p>
                    <hr>
                    <p class="mb-0">Sistem Informasi Akademik Siswa siap digunakan!</p>
                </div>
            </div>
        </div>

        <!-- Statistics Cards -->
        <div class="row mb-4">
            <div class="col-xl-3 col-md-6 mb-4">
                <div class="card border-left-primary shadow h-100 py-2">
                    <div class="card-body">
                        <div class="row no-gutters align-items-center">
                            <div class="col mr-2">
                                <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                    Total Siswa
                                </div>
                                <div class="h5 mb-0 font-weight-bold text-gray-800">
                                    <?php
                                    try {
                                        require_once 'app/models/Database.php';
                                        require_once 'app/models/Siswa.php';
                                        $siswaModel = new Siswa();
                                        $allSiswa = $siswaModel->getAll();
                                        echo count($allSiswa);
                                    } catch (Exception $e) {
                                        echo "0";
                                    }
                                    ?>
                                </div>
                            </div>
                            <div class="col-auto">
                                <i class="bi bi-people fa-2x text-gray-300"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-xl-3 col-md-6 mb-4">
                <div class="card border-left-success shadow h-100 py-2">
                    <div class="card-body">
                        <div class="row no-gutters align-items-center">
                            <div class="col mr-2">
                                <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                    Total Kelas
                                </div>
                                <div class="h5 mb-0 font-weight-bold text-gray-800">
                                    <?php
                                    try {
                                        require_once 'app/models/Kelas.php';
                                        $kelasModel = new Kelas();
                                        $allKelas = $kelasModel->getAll();
                                        echo count($allKelas);
                                    } catch (Exception $e) {
                                        echo "0";
                                    }
                                    ?>
                                </div>
                            </div>
                            <div class="col-auto">
                                <i class="bi bi-building fa-2x text-gray-300"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-xl-3 col-md-6 mb-4">
                <div class="card border-left-info shadow h-100 py-2">
                    <div class="card-body">
                        <div class="row no-gutters align-items-center">
                            <div class="col mr-2">
                                <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                    Status
                                </div>
                                <div class="h5 mb-0 font-weight-bold text-gray-800">
                                    Online
                                </div>
                            </div>
                            <div class="col-auto">
                                <i class="bi bi-check-circle fa-2x text-gray-300"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-xl-3 col-md-6 mb-4">
                <div class="card border-left-warning shadow h-100 py-2">
                    <div class="card-body">
                        <div class="row no-gutters align-items-center">
                            <div class="col mr-2">
                                <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                    User Role
                                </div>
                                <div class="h5 mb-0 font-weight-bold text-gray-800">
                                    <?= ucfirst($currentUser['role']) ?>
                                </div>
                            </div>
                            <div class="col-auto">
                                <i class="bi bi-person-badge fa-2x text-gray-300"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="row">
            <div class="col-12">
                <div class="card shadow">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">
                            <i class="bi bi-lightning"></i> Quick Actions
                        </h6>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-3 mb-3">
                                <a href="public/siswa" class="btn btn-primary btn-block">
                                    <i class="bi bi-people"></i><br>
                                    <small>Kelola Siswa</small>
                                </a>
                            </div>
                            <div class="col-md-3 mb-3">
                                <a href="public/kelas" class="btn btn-success btn-block">
                                    <i class="bi bi-building"></i><br>
                                    <small>Kelola Kelas</small>
                                </a>
                            </div>
                            <div class="col-md-3 mb-3">
                                <a href="debug_dashboard.php" class="btn btn-info btn-block">
                                    <i class="bi bi-bug"></i><br>
                                    <small>Debug Dashboard</small>
                                </a>
                            </div>
                            <div class="col-md-3 mb-3">
                                <a href="public/dashboard" class="btn btn-warning btn-block">
                                    <i class="bi bi-speedometer2"></i><br>
                                    <small>Full Dashboard</small>
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <style>
        .border-left-primary { border-left: 0.25rem solid #4e73df !important; }
        .border-left-success { border-left: 0.25rem solid #1cc88a !important; }
        .border-left-info { border-left: 0.25rem solid #36b9cc !important; }
        .border-left-warning { border-left: 0.25rem solid #f6c23e !important; }
        .text-gray-800 { color: #5a5c69 !important; }
        .text-gray-300 { color: #dddfeb !important; }
        .font-weight-bold { font-weight: 700 !important; }
        .text-xs { font-size: 0.7rem; }
        .fa-2x { font-size: 2em; }
        .btn-block { display: block; width: 100%; padding: 15px; text-align: center; }
    </style>
</body>
</html>
